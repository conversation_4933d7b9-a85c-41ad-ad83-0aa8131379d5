"""
TravelPro - Gestionnaire de session
-----------------------------------
Ce module gère la session utilisateur et la navigation entre les écrans.
"""

import logging
from PyQt6.QtWidgets import QApplication, QStackedWidget
from PyQt6.QtCore import QObject, pyqtSignal

from travelpro.core.auth import AuthManager
from travelpro.ui.login_screen import LoginScreen
from travelpro.ui.main_window import MainWindow

logger = logging.getLogger(__name__)

class SessionManager(QObject):
    """Gestionnaire de session pour TravelPro."""
    
    # Signal émis lors du changement d'état de la session
    session_changed = pyqtSignal(bool, str)  # is_logged_in, username
    
    _instance = None
    
    def __new__(cls):
        """Implémentation du pattern Singleton."""
        if cls._instance is None:
            cls._instance = super(SessionManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialisation du gestionnaire de session."""
        if self._initialized:
            return
            
        super().__init__()
        
        self.auth_manager = AuthManager()
        self.current_user = None
        self.current_token = None
        self.is_logged_in = False
        
        self._initialized = True
        logger.info("Gestionnaire de session initialisé")
    
    def start_session(self, app):
        """Démarre la session et configure l'interface utilisateur initiale."""
        # Création du widget empilé pour la navigation entre écrans
        self.stacked_widget = QStackedWidget()
        
        # Création des écrans
        self.login_screen = LoginScreen()
        self.main_window = MainWindow()
        
        # Ajout des écrans au widget empilé
        self.stacked_widget.addWidget(self.login_screen)
        self.stacked_widget.addWidget(self.main_window)
        
        # Connexion des signaux
        self.login_screen.login_successful.connect(self.on_login_successful)
        
        # Affichage de l'écran de connexion par défaut
        self.stacked_widget.setCurrentWidget(self.login_screen)
        self.stacked_widget.show()
        
        logger.info("Session démarrée")
        
        return self.stacked_widget
    
    def on_login_successful(self, username, token):
        """Gère la connexion réussie d'un utilisateur."""
        self.current_user = username
        self.current_token = token
        self.is_logged_in = True
        
        # Mise à jour de l'interface utilisateur
        self.main_window.user_name.setText(username)
        
        # Passage à la fenêtre principale
        self.stacked_widget.setCurrentWidget(self.main_window)
        
        # Émission du signal de changement de session
        self.session_changed.emit(True, username)
        
        logger.info(f"Session démarrée pour l'utilisateur: {username}")
    
    def logout(self):
        """Déconnecte l'utilisateur actuel."""
        if not self.is_logged_in:
            return
        
        self.current_user = None
        self.current_token = None
        self.is_logged_in = False
        
        # Passage à l'écran de connexion
        self.stacked_widget.setCurrentWidget(self.login_screen)
        
        # Réinitialisation des champs de connexion
        self.login_screen.username_input.clear()
        self.login_screen.password_input.clear()
        
        # Émission du signal de changement de session
        self.session_changed.emit(False, "")
        
        logger.info("Utilisateur déconnecté")
    
    def get_current_user(self):
        """Retourne l'utilisateur actuellement connecté."""
        return self.current_user
    
    def is_authenticated(self):
        """Vérifie si un utilisateur est actuellement connecté."""
        return self.is_logged_in
    
    def get_token(self):
        """Retourne le token JWT de l'utilisateur connecté."""
        return self.current_token
