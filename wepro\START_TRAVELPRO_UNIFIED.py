# -*- coding: utf-8 -*-
"""
TravelPro - Point de Démarrage Unifié
Point d'entrée unique pour éviter les erreurs et garantir un démarrage fluide.
"""

import sys
import os
import traceback

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import du gestionnaire d'erreurs en premier
try:
    from travelpro.utils.error_handler import (
        SafeImportManager, error_handler, safe_execute, log_info, log_error,
        UIErrorHandler
    )
    log_info("TravelPro - Démarrage avec gestionnaire d'erreurs")
except ImportError as e:
    print(f"Attention: Gestionnaire d'erreurs non disponible - {e}")
    # Fallback simple
    def log_info(msg): print(f"INFO: {msg}")
    def log_error(msg): print(f"ERROR: {msg}")
    def safe_execute(func, *args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"Erreur: {e}")
            return None

# Importations sécurisées PyQt5
log_info("Import des composants PyQt5...")
try:
    from PyQt5.QtWidgets import (
        QApplication, QMessageBox, QSplashScreen, QDialog, QVBoxLayout,
        QHBoxLayout, QPushButton, QLabel, QRadioButton, QButtonGroup
    )
    from PyQt5.QtCore import Qt, QTimer
    from PyQt5.QtGui import QPixmap, QPainter, QFont, QIcon
    log_info("PyQt5 importé avec succès")
except ImportError as e:
    log_error(f"Erreur critique: PyQt5 non disponible - {e}")
    print("ERREUR CRITIQUE: PyQt5 n'est pas installé.")
    print("Veuillez exécuter: python install_requirements_safe.py")
    sys.exit(1)

class LanguageSelector(QDialog):
    """Sélecteur de langue au démarrage."""

    def __init__(self):
        super().__init__()
        self.selected_language = "french"
        self.initUI()

    def initUI(self):
        self.setWindowTitle("TravelPro - Sélection de Langue")
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        layout = QVBoxLayout()

        # Titre
        title = QLabel("TravelPro")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin: 20px;
        """)
        layout.addWidget(title)

        # Sous-titre
        subtitle = QLabel("Système de Gestion d'Agence de Voyage")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setStyleSheet("""
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 30px;
        """)
        layout.addWidget(subtitle)

        # Sélection de langue
        lang_label = QLabel("Choisissez votre langue / Choose your language:")
        lang_label.setAlignment(Qt.AlignCenter)
        lang_label.setStyleSheet("font-size: 16px; margin-bottom: 20px;")
        layout.addWidget(lang_label)

        # Boutons radio pour les langues
        self.button_group = QButtonGroup()

        # Français
        self.french_radio = QRadioButton("🇫🇷 Français (Recommandé)")
        self.french_radio.setChecked(True)
        self.french_radio.setStyleSheet("font-size: 14px; padding: 10px;")
        self.button_group.addButton(self.french_radio)
        layout.addWidget(self.french_radio)

        # Arabe
        self.arabic_radio = QRadioButton("🇸🇦 العربية")
        self.arabic_radio.setStyleSheet("font-size: 14px; padding: 10px;")
        self.button_group.addButton(self.arabic_radio)
        layout.addWidget(self.arabic_radio)

        # Anglais
        self.english_radio = QRadioButton("🇬🇧 English")
        self.english_radio.setStyleSheet("font-size: 14px; padding: 10px;")
        self.button_group.addButton(self.english_radio)
        layout.addWidget(self.english_radio)

        layout.addStretch()

        # Boutons d'action
        button_layout = QHBoxLayout()

        self.start_button = QPushButton("Démarrer / Start")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.start_button.clicked.connect(self.start_application)

        self.exit_button = QPushButton("Quitter / Exit")
        self.exit_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 24px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        self.exit_button.clicked.connect(self.reject)

        button_layout.addWidget(self.exit_button)
        button_layout.addWidget(self.start_button)
        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Centrer la fenêtre
        self.center_window()

    def center_window(self):
        """Centrer la fenêtre sur l'écran."""
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def start_application(self):
        """Démarrer l'application avec la langue sélectionnée."""
        if self.french_radio.isChecked():
            self.selected_language = "french"
        elif self.arabic_radio.isChecked():
            self.selected_language = "arabic"
        elif self.english_radio.isChecked():
            self.selected_language = "english"

        self.accept()

def create_unified_splash_screen():
    """Créer un écran de démarrage unifié."""
    try:
        splash_pix = QPixmap(500, 350)
        splash_pix.fill(Qt.white)

        painter = QPainter(splash_pix)
        painter.setRenderHint(QPainter.Antialiasing)

        # Dégradé de fond
        from PyQt5.QtGui import QLinearGradient, QBrush, QColor
        gradient = QLinearGradient(0, 0, 500, 350)
        gradient.setColorAt(0, QColor("#1e3c72"))
        gradient.setColorAt(1, QColor("#2a5298"))
        painter.fillRect(splash_pix.rect(), QBrush(gradient))

        # Titre principal
        painter.setPen(Qt.white)
        painter.setFont(QFont("Arial", 28, QFont.Bold))
        painter.drawText(splash_pix.rect(), Qt.AlignCenter, "TravelPro\nSystème Unifié\nDémarrage Sécurisé")

        # Informations de version
        painter.setFont(QFont("Arial", 12))
        painter.drawText(50, 280, "Version 2.0.0 - Édition Unifiée")
        painter.drawText(50, 300, "Point de démarrage unique")
        painter.drawText(50, 320, "Chargement en cours...")

        painter.end()

        splash = QSplashScreen(splash_pix)
        return splash

    except Exception as e:
        print(f"Erreur création splash screen: {e}")
        return None

def check_system_requirements():
    """Vérifier les prérequis système."""
    try:
        print("🔍 Vérification des prérequis système...")

        # Vérifier Python
        if sys.version_info < (3, 6):
            raise Exception("Python 3.6+ requis")
        print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

        # Vérifier PyQt5
        try:
            from PyQt5.QtWidgets import QApplication
            from PyQt5 import QtCore
            print(f"✅ PyQt5 disponible (version {QtCore.PYQT_VERSION_STR})")
        except ImportError as e:
            raise Exception(f"PyQt5 non installé: {e}")

        # Vérifier SQLAlchemy
        try:
            import sqlalchemy
            print(f"✅ SQLAlchemy disponible (version {sqlalchemy.__version__})")
        except ImportError as e:
            raise Exception(f"SQLAlchemy non installé: {e}")

        # Vérifier qtawesome
        try:
            import qtawesome
            print("✅ QtAwesome disponible")
        except ImportError:
            print("⚠️ QtAwesome non disponible (icônes limitées)")

        # Vérifier l'environnement virtuel
        if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
            print("✅ Environnement virtuel actif")
        else:
            print("⚠️ Environnement virtuel non détecté")

        # Vérifier l'espace disque
        import shutil
        free_space = shutil.disk_usage('.').free / (1024**3)  # GB
        if free_space < 1:
            print("⚠️ Espace disque faible (< 1GB)")
        else:
            print(f"✅ Espace disque suffisant ({free_space:.1f}GB)")

        print("✅ Prérequis système validés")
        return True

    except Exception as e:
        print(f"❌ Erreur prérequis: {e}")
        return False

def setup_unified_database():
    """Configuration unifiée de la base de données."""
    try:
        print("📊 Configuration de la base de données...")

        # Import sécurisé des modules de base de données
        from database import init_db, get_db

        # Initialisation
        init_db()

        # Test de connexion
        db = next(get_db())
        db.close()

        print("✅ Base de données configurée avec succès")
        return True

    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
        return False

def create_unified_admin():
    """Création unifiée de l'administrateur."""
    try:
        print("👤 Configuration de l'administrateur...")

        from logic.auth import create_user, get_user_by_username
        from database import get_db

        db = next(get_db())
        try:
            if not get_user_by_username(db, "admin"):
                create_user(db, "admin", "admin123", "admin")
                print("✅ Administrateur créé: admin/admin123")
            else:
                print("ℹ️ Administrateur existant")
        finally:
            db.close()

        return True

    except Exception as e:
        print(f"⚠️ Erreur administrateur: {e}")
        return False

def load_unified_features():
    """Chargement unifié des fonctionnalités."""
    features = []

    try:
        from travelpro.ui.theme_manager import ThemeManager
        features.append("Gestionnaire de Thèmes")
    except ImportError:
        pass

    try:
        from travelpro.ui.advanced_charts import AdvancedDashboard
        features.append("Graphiques Avancés")
    except ImportError:
        pass

    try:
        from travelpro.ui.performance_optimizer import PerformanceOptimizer
        features.append("Optimiseur de Performance")
    except ImportError:
        pass

    try:
        from travelpro.ui.smart_analytics import SmartAnalytics
        features.append("Analytique Intelligente")
    except ImportError:
        pass

    print(f"✅ Fonctionnalités chargées: {len(features)}")
    for feature in features:
        print(f"   • {feature}")

    return features

def start_french_version():
    """Démarrer la version française."""
    try:
        print("🇫🇷 Démarrage version française...")

        # Configuration française
        from PyQt5.QtCore import QLocale
        QLocale.setDefault(QLocale(QLocale.French, QLocale.France))

        # Import des composants français
        from travelpro.ui.login_screen import LoginScreen
        from travelpro.ui.main_window import MainWindow
        from database import get_db, session_scope

        # Création des composants
        login_screen = LoginScreen(db_session_provider=get_db)
        main_window = MainWindow(db_session_provider=session_scope)

        # Configuration des transitions
        def show_main_after_login():
            login_screen.hide()
            main_window.show()
            def _welcome_fr():
                try:
                    from travelpro.core.translations import translation_manager as tr
                    _title = tr.get_system('welcome_title', 'Bienvenue')
                    _text = tr.get_system('welcome_message', 'Bienvenue dans TravelPro!\nVersion française complète.')
                except Exception:
                    _title = 'Bienvenue'
                    _text = 'Bienvenue dans TravelPro!\nVersion française complète.'
                QMessageBox.information(main_window, _title, _text)
            QTimer.singleShot(1000, _welcome_fr)

        login_screen.login_success.connect(show_main_after_login)

        print("✅ Version française prête")
        return login_screen, main_window

    except Exception as e:
        print(f"❌ Erreur version française: {e}")
        raise

def start_arabic_version():
    """Démarrer la version arabe."""
    try:
        print("🇸🇦 Démarrage version arabe...")

        # Import des composants arabes (version originale)
        from travelpro.ui.login_screen import LoginScreen
        from travelpro.ui.main_window import MainWindow
        from database import get_db, session_scope

        # Création des composants
        login_screen = LoginScreen(db_session_provider=get_db)
        main_window = MainWindow(db_session_provider=session_scope)

        # Configuration des transitions
        def show_main_after_login():
            login_screen.hide()
            main_window.show()
            def _welcome_ar():
                try:
                    from travelpro.core.translations import translation_manager as tr
                    _title = tr.get_system('welcome_title', 'مرحبًا')
                    _text = tr.get_system('welcome_message', 'مرحبًا بك في TravelPro!\nالإصدار الكامل.')
                except Exception:
                    _title = 'مرحبًا'
                    _text = 'مرحبًا بك في TravelPro!\nالإصدار الكامل.'
                QMessageBox.information(main_window, _title, _text)
            QTimer.singleShot(1000, _welcome_ar)

        login_screen.login_success.connect(show_main_after_login)

        print("✅ Version arabe prête")
        return login_screen, main_window

    except Exception as e:
        print(f"❌ Erreur version arabe: {e}")
        raise

def start_english_version():
    """Démarrer la version anglaise."""
    try:
        print("🇬🇧 Starting English version...")

        # Configuration anglaise
        from PyQt5.QtCore import QLocale
        QLocale.setDefault(QLocale(QLocale.English, QLocale.UnitedStates))

        # Import des composants (version de base)
        from travelpro.ui.login_screen import LoginScreen
        from travelpro.ui.main_window import MainWindow
        from database import get_db, session_scope

        # Création des composants
        login_screen = LoginScreen(db_session_provider=get_db)
        main_window = MainWindow(db_session_provider=session_scope)

        # Configuration des transitions
        def show_main_after_login():
            login_screen.hide()
            main_window.show()
            def _welcome_en():
                try:
                    from travelpro.core.translations import translation_manager as tr
                    _title = tr.get_system('welcome_title', 'Welcome')
                    _text = tr.get_system('welcome_message', 'Welcome to TravelPro!\nComplete English version.')
                except Exception:
                    _title = 'Welcome'
                    _text = 'Welcome to TravelPro!\nComplete English version.'
                QMessageBox.information(main_window, _title, _text)
            QTimer.singleShot(1000, _welcome_en)

        login_screen.login_success.connect(show_main_after_login)

        print("✅ English version ready")
        return login_screen, main_window

    except Exception as e:
        print(f"❌ Error English version: {e}")
        raise

def main():
    """Fonction principale unifiée."""
    print("🚀 TravelPro - Démarrage Unifié")
    print("=" * 50)

    # Configuration High DPI
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # Création de l'application
    app = QApplication(sys.argv)
    app.setApplicationName("TravelPro")
    app.setApplicationVersion("2.0.0 - Unified")
    app.setOrganizationName("TravelPro Agency")

    # Appliquer thème par défaut pour واجهة أنظف منذ البداية
    try:
        from travelpro.ui.theme_manager import ThemeManager
        ThemeManager().apply_theme(app)
        # حاول تطبيق الثيم المحفوظ من الإعدادات أولاً
        try:
            from travelpro.core.config import Config
            cfg = Config()
            key = cfg.get('ui.theme_key', None)
            if key:
                tm = ThemeManager()
                if tm.set_theme(key):
                    tm.apply_theme(app)
        except Exception:
            pass

    except Exception:
        pass

    try:
        # Vérification des prérequis
        if not check_system_requirements():
            QMessageBox.critical(None, "Erreur", "Prérequis système non satisfaits")
            return 1

        # Écran de démarrage
        splash = create_unified_splash_screen()
        if splash:
            splash.show()
            app.processEvents()

        # Configuration de la base de données
        if not setup_unified_database():
            if splash:
                splash.close()
            QMessageBox.critical(None, "Erreur", "Impossible de configurer la base de données")
            return 1

        # Création de l'administrateur
        create_unified_admin()

        # Chargement des fonctionnalités
        features = load_unified_features()

        # Sélection de la langue
        if splash:
            splash.close()

        # تجاوز اختيار اللغة إذا كانت محفوظة + خيار إظهار منتقي اللغة عند البدء
        try:
            from travelpro.core.config import Config
            cfg = Config()
            saved_lang = cfg.get('language', None)
            show_selector = cfg.get('ui.show_language_selector', False)
        except Exception:
            saved_lang = None
            show_selector = False

        if show_selector:
            # عرض منتقي اللغة عند البدء إذا كان مُفعّلاً من الإعدادات
            try:
                from travelpro.ui.language_selector import LanguageSelector
                dlg = LanguageSelector()
                if dlg.exec_() == QDialog.Accepted:
                    selected_language = dlg.selected_language  # 'arabic' | 'english' | 'french'
                    lang_map = {'arabic': 'ar', 'english': 'en', 'french': 'fr'}
                    try:
                        cfg.set('language', lang_map.get(selected_language, 'fr'))
                    except Exception:
                        pass
                else:
                    selected_language = 'french'
            except Exception:
                # في حال تعذر إظهار المنتقي، نعتمد على اللغة المحفوظة أو الفرنسية افتراضياً
                if saved_lang in ("ar", "en", "fr"):
                    selected_language = {"ar": "arabic", "en": "english", "fr": "french"}.get(saved_lang, "french")
                else:
                    selected_language = 'french'
                    try:
                        cfg.set('language', 'fr')
                    except Exception:
                        pass
        else:
            # بدون منتقي: نستخدم اللغة المحفوظة أو الفرنسية افتراضياً لأول تشغيل
            if saved_lang in ("ar", "en", "fr"):
                selected_language = {"ar": "arabic", "en": "english", "fr": "french"}.get(saved_lang, "french")
            else:
                selected_language = "french"
                try:
                    cfg.set('language', 'fr')
                except Exception:
                    pass

        # Configurer الترجمة واتجاه الواجهة حسب اللغة
        try:
            from travelpro.core.translations import translation_manager as tr
            from PyQt5.QtCore import QLocale
            if selected_language == "arabic":
                tr.set_language("ar")
                app.setLayoutDirection(Qt.RightToLeft)
                QLocale.setDefault(QLocale(QLocale.Arabic, QLocale.SaudiArabia))
            elif selected_language == "english":
                tr.set_language("en")
                app.setLayoutDirection(Qt.LeftToRight)
                QLocale.setDefault(QLocale(QLocale.English, QLocale.UnitedStates))
            else:
                tr.set_language("fr")
                app.setLayoutDirection(Qt.LeftToRight)
                QLocale.setDefault(QLocale(QLocale.French, QLocale.France))
            # حفظ اللغة المختارة إلى الإعدادات
            try:
                from travelpro.core.config import Config
                cfg = Config()
                lang_map = {"arabic": "ar", "english": "en", "french": "fr"}
                cfg.set('language', lang_map.get(selected_language, 'fr'))
            except Exception:
                pass
                app.setLayoutDirection(Qt.LeftToRight)
                QLocale.setDefault(QLocale(QLocale.French, QLocale.France))
        except Exception:
            pass

        # Nouvel écran de démarrage pour la langue sélectionnée
        splash = create_unified_splash_screen()
        if splash:
            splash.show()
            app.processEvents()

        # Démarrage selon la langue
        if selected_language == "french":
            login_screen, main_window = start_french_version()
        elif selected_language == "arabic":
            login_screen, main_window = start_arabic_version()
        elif selected_language == "english":
            login_screen, main_window = start_english_version()
        else:
            raise Exception("Langue non supportée")

        # Fermeture du splash et affichage
        if splash:
            splash.finish(login_screen)

        login_screen.show()

        print(f"✅ TravelPro démarré en {selected_language}")
        print("📋 Connexion: admin / admin123")
        print(f"🌟 Fonctionnalités: {len(features)}")

        return app.exec_()

    except Exception as e:
        print(f"💥 Erreur critique: {e}")
        traceback.print_exc()

        if 'splash' in locals() and splash:
            splash.close()

        QMessageBox.critical(None, "Erreur Critique", f"Erreur lors du démarrage:\n{e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
