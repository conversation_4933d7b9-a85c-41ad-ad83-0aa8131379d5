"""
TravelPro - Module de base de données
-------------------------------------
Ce module gère la connexion à la base de données et les opérations ORM.
"""

import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session

from travelpro.core.config import Config

logger = logging.getLogger(__name__)

# Base déclarative pour les modèles SQLAlchemy
Base = declarative_base()

class Database:
    """Gestionnaire de base de données pour TravelPro."""
    
    _instance = None
    
    def __new__(cls):
        """Implémentation du pattern Singleton."""
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialisation de la connexion à la base de données."""
        if self._initialized:
            return
            
        self.config = Config()
        self.db_path = self.config.get('db_path')
        
        # Création du répertoire parent si nécessaire
        db_dir = os.path.dirname(self.db_path)
        if not os.path.exists(db_dir):
            os.makedirs(db_dir)
            
        # Création de l'URL de connexion SQLite
        self.db_url = f"sqlite:///{self.db_path}"
        
        # Création du moteur SQLAlchemy
        self.engine = create_engine(
            self.db_url,
            connect_args={"check_same_thread": False},  # Nécessaire pour SQLite
            echo=(self.config.get('log_level') == 'DEBUG')  # Activer les logs SQL en mode DEBUG
        )
        
        # Création de la factory de session
        self.session_factory = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Session thread-safe
        self.Session = scoped_session(self.session_factory)
        
        self._initialized = True
        logger.info(f"Connexion à la base de données initialisée: {self.db_url}")
    
    def create_tables(self):
        """Crée toutes les tables définies dans les modèles."""
        Base.metadata.create_all(self.engine)
        logger.info("Tables créées dans la base de données")
    
    def get_session(self):
        """Retourne une nouvelle session de base de données."""
        return self.Session()
    
    def close_session(self, session):
        """Ferme proprement une session de base de données."""
        if session:
            session.close()
    
    def init_db(self):
        """Initialise la base de données avec les tables et données de base."""
        self.create_tables()
        
        # Ici, on pourrait ajouter du code pour insérer des données initiales
        # comme un utilisateur admin par défaut, des paramètres système, etc.
        
        logger.info("Base de données initialisée avec succès")
