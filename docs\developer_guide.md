"""
TravelPro - Guide de développement
----------------------------------
Ce document fournit une documentation complète sur la structure et l'utilisation du code source de TravelPro.
"""

# Guide de développement TravelPro

## Introduction

TravelPro est une application de bureau développée en Python pour Windows, destinée aux agences de voyages et tour-opérateurs. Cette documentation fournit les informations nécessaires pour comprendre, utiliser et étendre le code source de l'application.

## Structure du projet

Le projet est organisé selon une architecture MVC (Modèle-Vue-Contrôleur) modulaire :

```
travelpro/
├── docs/                  # Documentation
├── tests/                 # Tests unitaires et d'intégration
├── travelpro/             # Code source principal
│   ├── core/              # Fonctionnalités de base
│   │   ├── __init__.py
│   │   ├── config.py      # Gestion de la configuration
│   │   ├── database.py    # Connexion à la base de données
│   │   ├── auth.py        # Authentification et autorisations
│   │   └── session.py     # Gestion de session utilisateur
│   ├── controllers/       # Contrôleurs (logique métier)
│   ├── models/            # Modèles de données
│   │   ├── __init__.py
│   │   ├── client.py      # Modèles pour les clients et fidélité
│   │   └── reservation.py # Modèles pour les réservations et produits
│   ├── ui/                # Interface utilisateur
│   │   ├── __init__.py
│   │   ├── main_window.py # Fenêtre principale
│   │   └── login_screen.py # Écran de connexion
│   ├── utils/             # Utilitaires
│   ├── resources/         # Ressources (images, icônes, etc.)
│   ├── __init__.py        # Initialisation du package
│   └── __main__.py        # Point d'entrée principal
├── requirements.txt       # Dépendances Python
└── README.md              # Documentation générale
```

## Modules principaux

### Core

Le module `core` contient les fonctionnalités fondamentales de l'application :

#### Configuration (`config.py`)

La classe `Config` gère la configuration de l'application, stockée dans un fichier JSON. Elle implémente le pattern Singleton pour garantir une instance unique.

```python
from travelpro.core.config import Config

# Obtenir une instance de la configuration
config = Config()

# Récupérer une valeur
theme = config.get('theme', 'default')

# Définir une valeur
config.set('language', 'fr')

# Réinitialiser la configuration
config.reset()
```

#### Base de données (`database.py`)

La classe `Database` gère la connexion à la base de données SQLite et fournit des méthodes pour interagir avec elle via SQLAlchemy.

```python
from travelpro.core.database import Database

# Obtenir une instance de la base de données
db = Database()

# Créer les tables
db.create_tables()

# Obtenir une session
session = db.get_session()
try:
    # Utiliser la session...
    session.commit()
except Exception as e:
    session.rollback()
    raise e
finally:
    db.close_session(session)
```

#### Authentification (`auth.py`)

La classe `AuthManager` gère l'authentification des utilisateurs et les autorisations.

```python
from travelpro.core.auth import AuthManager

# Obtenir une instance du gestionnaire d'authentification
auth_manager = AuthManager()

# Créer un utilisateur
user = auth_manager.create_user(
    username="admin",
    email="<EMAIL>",
    password="password123",
    full_name="Administrateur",
    is_admin=True
)

# Authentifier un utilisateur
token = auth_manager.authenticate("admin", "password123")

# Vérifier un token
payload = auth_manager.verify_token(token)
if payload:
    user_id = payload['user_id']
    username = payload['username']
    is_admin = payload['is_admin']
```

#### Session (`session.py`)

La classe `SessionManager` gère la session utilisateur et la navigation entre les écrans.

```python
from travelpro.core.session import SessionManager

# Obtenir une instance du gestionnaire de session
session_manager = SessionManager()

# Démarrer la session
app = QApplication(sys.argv)
main_widget = session_manager.start_session(app)
main_widget.show()

# Vérifier si un utilisateur est connecté
if session_manager.is_authenticated():
    username = session_manager.get_current_user()

# Déconnecter l'utilisateur
session_manager.logout()
```

### Modèles

Les modèles définissent la structure des données de l'application :

#### Client (`models/client.py`)

Modèles pour les clients et la fidélisation :

- `Client` : Informations sur les clients
- `LoyaltyCard` : Cartes de fidélité
- `LoyaltyTransaction` : Transactions de points de fidélité

```python
from travelpro.models.client import Client, LoyaltyCard
from travelpro.core.database import Database

# Créer un client
db = Database()
session = db.get_session()
try:
    client = Client(
        code="CLI001",
        civility="M.",
        first_name="Jean",
        last_name="Dupont",
        email="<EMAIL>",
        phone="+33123456789"
    )
    session.add(client)
    session.commit()
    
    # Créer une carte de fidélité
    loyalty_card = LoyaltyCard(
        card_number="FIDELITY001",
        client_id=client.id,
        status="Gold"
    )
    session.add(loyalty_card)
    session.commit()
except Exception as e:
    session.rollback()
    raise e
finally:
    db.close_session(session)
```

#### Réservation (`models/reservation.py`)

Modèles pour les réservations et les produits :

- `Reservation` : Réservations des clients
- `ReservationItem` : Éléments d'une réservation
- `Product` : Produits touristiques
- `Payment` : Paiements associés aux réservations

```python
from travelpro.models.reservation import Reservation, ReservationItem, Product, Payment, ReservationStatus
from travelpro.core.database import Database
import datetime

# Créer un produit
db = Database()
session = db.get_session()
try:
    product = Product(
        code="PROD001",
        name="Séjour Paris",
        category="Séjour",
        description="Séjour à Paris pour 2 personnes",
        price=500.0
    )
    session.add(product)
    session.commit()
    
    # Créer une réservation
    reservation = Reservation(
        reference="RES001",
        client_id=1,  # ID d'un client existant
        status=ReservationStatus.CONFIRMED,
        total_amount=500.0,
        start_date=datetime.datetime.now(),
        end_date=datetime.datetime.now() + datetime.timedelta(days=7)
    )
    session.add(reservation)
    session.commit()
    
    # Ajouter un élément à la réservation
    item = ReservationItem(
        reservation_id=reservation.id,
        product_id=product.id,
        quantity=1,
        unit_price=500.0,
        description="Séjour Paris - 1 semaine"
    )
    session.add(item)
    session.commit()
except Exception as e:
    session.rollback()
    raise e
finally:
    db.close_session(session)
```

### Interface utilisateur

L'interface utilisateur est développée avec PyQt6 :

#### Fenêtre principale (`ui/main_window.py`)

La classe `MainWindow` définit la fenêtre principale de l'application avec une barre latérale, des onglets et un menu.

```python
from travelpro.ui.main_window import MainWindow
from PyQt6.QtWidgets import QApplication
import sys

# Créer et afficher la fenêtre principale
app = QApplication(sys.argv)
window = MainWindow()
window.show()
sys.exit(app.exec())
```

#### Écran de connexion (`ui/login_screen.py`)

La classe `LoginScreen` définit l'écran de connexion de l'application.

```python
from travelpro.ui.login_screen import LoginScreen
from PyQt6.QtWidgets import QApplication
import sys

# Créer et afficher l'écran de connexion
app = QApplication(sys.argv)
login = LoginScreen()
login.show()
sys.exit(app.exec())
```

## Point d'entrée principal

Le fichier `__main__.py` est le point d'entrée principal de l'application. Il initialise les composants nécessaires et lance l'interface utilisateur.

```python
# Exécuter l'application
python -m travelpro
```

## Tests unitaires

Les tests unitaires sont organisés dans le répertoire `tests/` et peuvent être exécutés avec pytest :

```bash
# Exécuter tous les tests
pytest tests/

# Exécuter un test spécifique
pytest tests/test_config.py
```

## Bonnes pratiques de développement

### Conventions de code

- Suivre PEP 8 pour le style de code Python
- Utiliser des docstrings pour documenter les fonctions et classes
- Préfixer les attributs privés par un underscore (_)

### Architecture

- Respecter le pattern MVC
- Utiliser les patterns de conception appropriés (Singleton, Repository, etc.)
- Séparer clairement les responsabilités entre les modules

### Gestion des erreurs

- Utiliser des blocs try/except pour gérer les exceptions
- Journaliser les erreurs avec le module logging
- Fournir des messages d'erreur clairs à l'utilisateur

## Conclusion

Cette documentation fournit une vue d'ensemble du code source de TravelPro. Pour plus de détails sur chaque module, consultez les docstrings dans le code source.
