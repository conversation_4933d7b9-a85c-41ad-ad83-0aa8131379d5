"""
TravelPro - Tests unitaires pour le module de configuration
----------------------------------------------------------
Ce module contient les tests unitaires pour le module de configuration.
"""

import os
import unittest
import tempfile
import json
from pathlib import Path

from travelpro.core.config import Config

class TestConfig(unittest.TestCase):
    """Tests unitaires pour la classe Config."""
    
    def setUp(self):
        """Préparation des tests."""
        # Sauvegarde du répertoire de configuration d'origine
        self.original_config_dir = os.path.join(str(Path.home()), '.travelpro')
        
        # Création d'un répertoire temporaire pour les tests
        self.temp_dir = tempfile.TemporaryDirectory()
        
        # Modification du répertoire de configuration pour les tests
        Config._instance = None
        self.config = Config()
        self.config._config_dir = self.temp_dir.name
        self.config._config_file = os.path.join(self.temp_dir.name, 'config.json')
        self.config._defaults = {
            'db_path': os.path.join(self.temp_dir.name, 'travelpro.db'),
            'theme': 'light',
            'language': 'fr',
            'backup_dir': os.path.join(self.temp_dir.name, 'backups'),
            'log_level': 'INFO'
        }
        self.config._config = {}
        self.config._load_config()
    
    def tearDown(self):
        """Nettoyage après les tests."""
        # Suppression du répertoire temporaire
        self.temp_dir.cleanup()
        
        # Réinitialisation de l'instance singleton
        Config._instance = None
    
    def test_singleton(self):
        """Teste que Config est bien un singleton."""
        config1 = Config()
        config2 = Config()
        self.assertIs(config1, config2)
    
    def test_default_config(self):
        """Teste que la configuration par défaut est correctement chargée."""
        # Vérification des valeurs par défaut
        self.assertEqual(self.config.get('theme'), 'light')
        self.assertEqual(self.config.get('language'), 'fr')
        self.assertEqual(self.config.get('log_level'), 'INFO')
        
        # Vérification que le fichier de configuration a été créé
        self.assertTrue(os.path.exists(self.config._config_file))
    
    def test_get_set_config(self):
        """Teste les méthodes get et set."""
        # Test de get avec une valeur existante
        self.assertEqual(self.config.get('theme'), 'light')
        
        # Test de get avec une valeur inexistante et une valeur par défaut
        self.assertEqual(self.config.get('inexistant', 'valeur_defaut'), 'valeur_defaut')
        
        # Test de set
        self.config.set('theme', 'dark')
        self.assertEqual(self.config.get('theme'), 'dark')
        
        # Vérification que la valeur a bien été sauvegardée dans le fichier
        with open(self.config._config_file, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        self.assertEqual(saved_config['theme'], 'dark')
    
    def test_reset_config(self):
        """Teste la méthode reset."""
        # Modification de plusieurs valeurs
        self.config.set('theme', 'dark')
        self.config.set('language', 'en')
        
        # Réinitialisation
        self.config.reset()
        
        # Vérification que les valeurs ont été réinitialisées
        self.assertEqual(self.config.get('theme'), 'light')
        self.assertEqual(self.config.get('language'), 'fr')
        
        # Vérification que le fichier a été mis à jour
        with open(self.config._config_file, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        self.assertEqual(saved_config['theme'], 'light')
        self.assertEqual(saved_config['language'], 'fr')


if __name__ == '__main__':
    unittest.main()
