"""
TravelPro - Tests unitaires pour le module d'authentification
------------------------------------------------------------
Ce module contient les tests unitaires pour le module d'authentification.
"""

import unittest
import jwt
from unittest.mock import patch, MagicMock

from travelpro.core.auth import AuthManager, User

class TestAuthManager(unittest.TestCase):
    """Tests unitaires pour la classe AuthManager."""
    
    def setUp(self):
        """Préparation des tests."""
        # Réinitialisation de l'instance singleton
        AuthManager._instance = None
        
        # Création d'un mock pour la base de données
        self.db_mock = MagicMock()
        self.session_mock = MagicMock()
        self.db_mock.get_session.return_value = self.session_mock
        
        # Création de l'instance à tester avec le mock
        with patch('travelpro.core.auth.Database', return_value=self.db_mock):
            self.auth_manager = AuthManager()
            self.auth_manager.secret_key = "test_secret_key"
    
    def test_singleton(self):
        """Teste que AuthManager est bien un singleton."""
        with patch('travelpro.core.auth.Database', return_value=self.db_mock):
            auth1 = AuthManager()
            auth2 = AuthManager()
            self.assertIs(auth1, auth2)
    
    def test_create_user_success(self):
        """Teste la création d'un utilisateur avec succès."""
        # Configuration du mock pour simuler un nouvel utilisateur
        self.session_mock.query.return_value.filter.return_value.first.return_value = None
        
        # Appel de la méthode à tester
        result = self.auth_manager.create_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
            full_name="Test User",
            is_admin=False
        )
        
        # Vérifications
        self.assertIsNotNone(result)
        self.session_mock.add.assert_called_once()
        self.session_mock.commit.assert_called_once()
    
    def test_create_user_existing(self):
        """Teste la création d'un utilisateur déjà existant."""
        # Configuration du mock pour simuler un utilisateur existant
        self.session_mock.query.return_value.filter.return_value.first.return_value = MagicMock()
        
        # Appel de la méthode à tester
        result = self.auth_manager.create_user(
            username="existinguser",
            email="<EMAIL>",
            password="password123"
        )
        
        # Vérifications
        self.assertIsNone(result)
        self.session_mock.add.assert_not_called()
        self.session_mock.commit.assert_not_called()
    
    def test_authenticate_success(self):
        """Teste l'authentification réussie d'un utilisateur."""
        # Création d'un utilisateur mock avec un mot de passe valide
        user_mock = MagicMock(spec=User)
        user_mock.id = 1
        user_mock.username = "testuser"
        user_mock.is_admin = False
        user_mock.is_active = True
        user_mock.verify_password.return_value = True
        
        # Configuration du mock pour retourner l'utilisateur
        self.session_mock.query.return_value.filter.return_value.first.return_value = user_mock
        
        # Appel de la méthode à tester
        token = self.auth_manager.authenticate("testuser", "password123")
        
        # Vérifications
        self.assertIsNotNone(token)
        
        # Décodage du token pour vérifier son contenu
        payload = jwt.decode(token, "test_secret_key", algorithms=['HS256'])
        self.assertEqual(payload['user_id'], 1)
        self.assertEqual(payload['username'], "testuser")
        self.assertEqual(payload['is_admin'], False)
    
    def test_authenticate_invalid_password(self):
        """Teste l'authentification avec un mot de passe invalide."""
        # Création d'un utilisateur mock avec un mot de passe invalide
        user_mock = MagicMock(spec=User)
        user_mock.is_active = True
        user_mock.verify_password.return_value = False
        
        # Configuration du mock pour retourner l'utilisateur
        self.session_mock.query.return_value.filter.return_value.first.return_value = user_mock
        
        # Appel de la méthode à tester
        token = self.auth_manager.authenticate("testuser", "wrongpassword")
        
        # Vérifications
        self.assertIsNone(token)
    
    def test_authenticate_inactive_user(self):
        """Teste l'authentification avec un utilisateur inactif."""
        # Création d'un utilisateur mock inactif
        user_mock = MagicMock(spec=User)
        user_mock.is_active = False
        
        # Configuration du mock pour retourner l'utilisateur
        self.session_mock.query.return_value.filter.return_value.first.return_value = user_mock
        
        # Appel de la méthode à tester
        token = self.auth_manager.authenticate("inactiveuser", "password123")
        
        # Vérifications
        self.assertIsNone(token)
    
    def test_verify_token_valid(self):
        """Teste la vérification d'un token valide."""
        # Création d'un token valide
        payload = {
            'user_id': 1,
            'username': "testuser",
            'is_admin': False
        }
        token = jwt.encode(payload, "test_secret_key", algorithm='HS256')
        
        # Appel de la méthode à tester
        result = self.auth_manager.verify_token(token)
        
        # Vérifications
        self.assertIsNotNone(result)
        self.assertEqual(result['user_id'], 1)
        self.assertEqual(result['username'], "testuser")
        self.assertEqual(result['is_admin'], False)
    
    def test_verify_token_invalid(self):
        """Teste la vérification d'un token invalide."""
        # Création d'un token avec une clé différente
        payload = {'user_id': 1, 'username': "testuser"}
        token = jwt.encode(payload, "wrong_secret_key", algorithm='HS256')
        
        # Appel de la méthode à tester
        result = self.auth_manager.verify_token(token)
        
        # Vérifications
        self.assertIsNone(result)


if __name__ == '__main__':
    unittest.main()
