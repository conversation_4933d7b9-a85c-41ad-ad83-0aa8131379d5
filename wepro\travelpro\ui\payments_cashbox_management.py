"""
Payments and Cashbox Management System for TravelPro
Complete payment tracking, cash management, and financial operations
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTableWidget,
    QTableWidgetItem, QHeaderView, QComboBox, QFormLayout, QGroupBox,
    QLineEdit, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox, QCheckBox,
    QMessageBox, QDialog, QDialogButtonBox, QTabWidget, QFrame, QScrollArea,
    QFileDialog, QProgressBar, QSplitter
)
from PyQt5.QtCore import Qt, QDate, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QFont, QPixmap
import qtawesome as qta
from datetime import datetime, timedelta
import json
import os


# Translations helper
try:
    from travelpro.core.translations import translation_manager as tr
except Exception:
    class _TR:
        def get_payment(self, k, d=None): return d or k
    tr = _TR()


class Payment:
    """Payment data model."""

    def __init__(self, payment_id=None, client_id=None, invoice_id=None,
                 amount=0.0, payment_method="cash", payment_date=None,
                 reference="", notes="", status="completed",
                 bank_account="", created_at=None):
        self.id = payment_id
        self.client_id = client_id
        self.invoice_id = invoice_id
        self.amount = amount
        self.payment_method = payment_method  # cash, card, transfer, check, other
        self.payment_date = payment_date or datetime.now().date()
        self.reference = reference
        self.notes = notes
        self.status = status  # pending, completed, failed, cancelled
        self.bank_account = bank_account
        self.created_at = created_at or datetime.now()
        self.updated_at = datetime.now()

    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'client_id': self.client_id,
            'invoice_id': self.invoice_id,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'payment_date': self.payment_date.isoformat() if self.payment_date else None,
            'reference': self.reference,
            'notes': self.notes,
            'status': self.status,
            'bank_account': self.bank_account,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def from_dict(cls, data):
        """Create from dictionary."""
        payment = cls()
        payment.__dict__.update(data)

        # Convert date strings back to date/datetime objects
        if payment.payment_date and isinstance(payment.payment_date, str):
            payment.payment_date = datetime.fromisoformat(payment.payment_date).date()
        if payment.created_at and isinstance(payment.created_at, str):
            payment.created_at = datetime.fromisoformat(payment.created_at)
        if payment.updated_at and isinstance(payment.updated_at, str):
            payment.updated_at = datetime.fromisoformat(payment.updated_at)

        return payment


class CashTransaction:
    """Cash transaction model for cashbox management."""

    def __init__(self, transaction_id=None, transaction_type="in", amount=0.0,
                 description="", category="", payment_id=None,
                 transaction_date=None, created_at=None):
        self.id = transaction_id
        self.transaction_type = transaction_type  # in, out
        self.amount = amount
        self.description = description
        self.category = category  # payment, expense, transfer, adjustment
        self.payment_id = payment_id  # Link to payment if applicable
        self.transaction_date = transaction_date or datetime.now().date()
        self.created_at = created_at or datetime.now()

    def to_dict(self):
        """Convert to dictionary."""
        return {
            'id': self.id,
            'transaction_type': self.transaction_type,
            'amount': self.amount,
            'description': self.description,
            'category': self.category,
            'payment_id': self.payment_id,
            'transaction_date': self.transaction_date.isoformat() if self.transaction_date else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @classmethod
    def from_dict(cls, data):
        """Create from dictionary."""
        transaction = cls()
        transaction.__dict__.update(data)

        # Convert date strings back to date/datetime objects
        if transaction.transaction_date and isinstance(transaction.transaction_date, str):
            transaction.transaction_date = datetime.fromisoformat(transaction.transaction_date).date()
        if transaction.created_at and isinstance(transaction.created_at, str):
            transaction.created_at = datetime.fromisoformat(transaction.created_at)

        return transaction


class PaymentManager:
    """Manager for payment operations."""

    def __init__(self, storage_file="payments.json"):
        self.storage_file = storage_file
        self.payments = []
        self.load_payments()

    def load_payments(self):
        """Load payments from storage."""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.payments = [Payment.from_dict(item) for item in data]
        except Exception as e:
            print(f"Error loading payments: {e}")
            self.payments = []

    def save_payments(self):
        """Save payments to storage."""
        try:
            data = [payment.to_dict() for payment in self.payments]
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving payments: {e}")

    def create_payment(self, payment):
        """Create a new payment."""
        payment.id = len(self.payments) + 1
        self.payments.append(payment)
        self.save_payments()
        return payment

    def update_payment(self, payment):
        """Update an existing payment."""
        for i, existing_payment in enumerate(self.payments):
            if existing_payment.id == payment.id:
                payment.updated_at = datetime.now()
                self.payments[i] = payment
                self.save_payments()
                return True
        return False

    def delete_payment(self, payment_id):
        """Delete a payment."""
        self.payments = [p for p in self.payments if p.id != payment_id]
        self.save_payments()

    def get_payments_by_method(self, method):
        """Get payments by method."""
        return [p for p in self.payments if p.payment_method == method]

    def get_payments_by_date_range(self, start_date, end_date):
        """Get payments within date range."""
        return [p for p in self.payments
                if start_date <= p.payment_date <= end_date]

    def get_total_by_method(self, method):
        """Get total amount by payment method."""
        return sum(p.amount for p in self.payments
                  if p.payment_method == method and p.status == "completed")


class CashboxManager:
    """Manager for cashbox operations."""

    def __init__(self, storage_file="cashbox.json"):
        self.storage_file = storage_file
        self.transactions = []
        self.load_transactions()

    def load_transactions(self):
        """Load transactions from storage."""
        try:
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.transactions = [CashTransaction.from_dict(item) for item in data]
        except Exception as e:
            print(f"Error loading cashbox transactions: {e}")
            self.transactions = []

    def save_transactions(self):
        """Save transactions to storage."""
        try:
            data = [transaction.to_dict() for transaction in self.transactions]
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving cashbox transactions: {e}")

    def add_transaction(self, transaction):
        """Add a new transaction."""
        transaction.id = len(self.transactions) + 1
        self.transactions.append(transaction)
        self.save_transactions()
        return transaction

    def get_balance(self):
        """Calculate current cash balance."""
        balance = 0
        for transaction in self.transactions:
            if transaction.transaction_type == "in":
                balance += transaction.amount
            else:
                balance -= transaction.amount
        return balance

    def get_transactions_by_date(self, date):
        """Get transactions for a specific date."""
        return [t for t in self.transactions if t.transaction_date == date]

    def get_transactions_by_category(self, category):
        """Get transactions by category."""
        return [t for t in self.transactions if t.category == category]


class PaymentDialog(QDialog):
    """Dialog for creating/editing payments."""

    def __init__(self, db_session_provider, payment=None, parent=None):
        super().__init__(parent)
        self.db_session_provider = db_session_provider
        self.payment = payment or Payment()

        self.setWindowTitle("Enregistrer Paiement")
        self.setModal(True)
        self.resize(500, 400)

        self.initUI()

        if payment:
            self.populate_form()

    def initUI(self):
        """Initialize the dialog UI."""
        layout = QVBoxLayout()

        # Payment details
        details_group = QGroupBox("Détails du Paiement")
        details_layout = QFormLayout()

        # Amount
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 999999)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" €")
        details_layout.addRow("Montant:", self.amount_input)

        # Payment method
        self.method_combo = QComboBox()
        self.method_combo.addItems([
            "Espèces", "Carte Bancaire", "Virement", "Chèque", "Autre"
        ])
        details_layout.addRow("Méthode:", self.method_combo)

        # Payment date
        self.payment_date = QDateEdit()
        self.payment_date.setCalendarPopup(True)
        self.payment_date.setDate(QDate.currentDate())
        details_layout.addRow("Date:", self.payment_date)

        # Reference
        self.reference_input = QLineEdit()
        try:
            from travelpro.core.translations import translation_manager as tr
            self.reference_input.setPlaceholderText(tr.get_payment('reference', 'Numéro de référence, chèque, etc.'))
            details_layout.addRow(tr.get_payment('reference', 'Référence') + ":", self.reference_input)
        except Exception:
            self.reference_input.setPlaceholderText("Numéro de référence, chèque, etc.")
            details_layout.addRow("Référence:", self.reference_input)

        # Bank account (for transfers)
        self.bank_account_input = QLineEdit()
        try:
            from travelpro.core.translations import translation_manager as tr
            self.bank_account_input.setPlaceholderText(tr.get_payment('bank_account', 'Compte bancaire (pour virements)'))
            details_layout.addRow(tr.get_payment('bank_account', 'Compte Bancaire') + ":", self.bank_account_input)
        except Exception:
            self.bank_account_input.setPlaceholderText("Compte bancaire (pour virements)")
            details_layout.addRow("Compte Bancaire:", self.bank_account_input)

        # Status
        self.status_combo = QComboBox()
        try:
            from travelpro.core.translations import translation_manager as tr
            self.status_combo.addItems([
                tr.get_payment('pending', 'En Attente'),
                tr.get_payment('completed', 'Complété'),
                tr.get_payment('failed', 'Échoué'),
                tr.get_payment('cancelled', 'Annulé'),
            ])
        except Exception:
            self.status_combo.addItems(["En Attente", "Complété", "Échoué", "Annulé"])
        self.status_combo.setCurrentIndex(1)  # Default to completed
        try:
            from travelpro.core.translations import translation_manager as tr
            details_layout.addRow(tr.get_payment('status', 'Statut') + ":", self.status_combo)
        except Exception:
            details_layout.addRow("Statut:", self.status_combo)

        details_group.setLayout(details_layout)
        layout.addWidget(details_group)

        # Notes
        try:
            from travelpro.core.translations import translation_manager as tr
            notes_group = QGroupBox(tr.get_main('notes', 'Notes'))
        except Exception:
            notes_group = QGroupBox("Notes")
        notes_layout = QVBoxLayout()

        self.notes_input = QTextEdit()
        self.notes_input.setMaximumHeight(80)
        try:
            from travelpro.core.translations import translation_manager as tr
            self.notes_input.setPlaceholderText(tr.get_main('notes', 'Notes additionnelles...'))
        except Exception:
            self.notes_input.setPlaceholderText("Notes additionnelles...")
        notes_layout.addWidget(self.notes_input)

        notes_group.setLayout(notes_layout)
        layout.addWidget(notes_group)

        # Cash transaction option
        self.add_to_cashbox = QCheckBox("Ajouter à la caisse (pour paiements en espèces)")
        self.add_to_cashbox.setChecked(True)
        layout.addWidget(self.add_to_cashbox)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_payment)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)
        self.setLayout(layout)

        # Connect method change to show/hide bank account
        self.method_combo.currentTextChanged.connect(self.on_method_changed)

    def on_method_changed(self, method):
        """Handle payment method change."""
        # Show bank account field only for transfers
        self.bank_account_input.setVisible(method == "Virement")

        # Auto-check cashbox for cash payments
        self.add_to_cashbox.setChecked(method == "Espèces")

    def populate_form(self):
        """Populate form with payment data."""
        self.amount_input.setValue(self.payment.amount)

        # Set payment method
        method_map = {
            "cash": "Espèces",
            "card": "Carte Bancaire",
            "transfer": "Virement",
            "check": "Chèque",
            "other": "Autre"
        }
        method_text = method_map.get(self.payment.payment_method, "Espèces")
        index = self.method_combo.findText(method_text)
        if index >= 0:
            self.method_combo.setCurrentIndex(index)

        self.payment_date.setDate(QDate.fromString(self.payment.payment_date.isoformat(), Qt.ISODate))
        self.reference_input.setText(self.payment.reference)
        self.bank_account_input.setText(self.payment.bank_account)

        # Set status
        status_map = {
            "pending": "En Attente",
            "completed": "Complété",
            "failed": "Échoué",
            "cancelled": "Annulé"
        }
        status_text = status_map.get(self.payment.status, "Complété")
        index = self.status_combo.findText(status_text)
        if index >= 0:
            self.status_combo.setCurrentIndex(index)

        self.notes_input.setPlainText(self.payment.notes)

    def save_payment(self):
        """Save the payment."""
        # Validate amount
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "Validation", "Le montant doit être supérieur à 0.")
            return

        # Update payment data
        self.payment.amount = self.amount_input.value()

        # Map payment method
        method_map = {
            "Espèces": "cash",
            "Carte Bancaire": "card",
            "Virement": "transfer",
            "Chèque": "check",
            "Autre": "other"
        }
        self.payment.payment_method = method_map.get(self.method_combo.currentText(), "cash")

        self.payment.payment_date = self.payment_date.date().toPyDate()
        self.payment.reference = self.reference_input.text().strip()
        self.payment.bank_account = self.bank_account_input.text().strip()

        # Map status
        status_map = {
            "En Attente": "pending",
            "Complété": "completed",
            "Échoué": "failed",
            "Annulé": "cancelled"
        }
        self.payment.status = status_map.get(self.status_combo.currentText(), "completed")

        self.payment.notes = self.notes_input.toPlainText().strip()

        self.accept()

    def get_payment(self):
        """Get the payment data."""
        return self.payment

    def should_add_to_cashbox(self):
        """Check if payment should be added to cashbox."""
        return self.add_to_cashbox.isChecked()


class CashTransactionDialog(QDialog):
    """Dialog for adding cash transactions."""

    def __init__(self, transaction=None, parent=None):
        super().__init__(parent)
        self.transaction = transaction or CashTransaction()

        self.setWindowTitle("Transaction de Caisse")
        self.setModal(True)
        self.resize(400, 300)

        self.initUI()

        if transaction:
            self.populate_form()

    def initUI(self):
        """Initialize the dialog UI."""
        layout = QVBoxLayout()

        # Transaction details
        details_group = QGroupBox("Détails de la Transaction")
        details_layout = QFormLayout()

        # Type
        self.type_combo = QComboBox()
        self.type_combo.addItems(["Entrée", "Sortie"])
        details_layout.addRow("Type:", self.type_combo)

        # Amount
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 999999)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" €")
        details_layout.addRow("Montant:", self.amount_input)

        # Category
        self.category_combo = QComboBox()
        self.category_combo.addItems([
            "Paiement Client", "Dépense", "Transfert", "Ajustement", "Autre"
        ])
        details_layout.addRow("Catégorie:", self.category_combo)

        # Description
        self.description_input = QLineEdit()
        try:
            from travelpro.core.translations import translation_manager as tr
            self.description_input.setPlaceholderText(tr.get_payment('description', 'Description de la transaction'))
            details_layout.addRow(tr.get_payment('description', 'Description') + ":", self.description_input)
        except Exception:
            self.description_input.setPlaceholderText("Description de la transaction")
            details_layout.addRow("Description:", self.description_input)

        # Date
        self.transaction_date = QDateEdit()
        self.transaction_date.setCalendarPopup(True)
        self.transaction_date.setDate(QDate.currentDate())
        details_layout.addRow("Date:", self.transaction_date)

        details_group.setLayout(details_layout)
        layout.addWidget(details_group)

        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Save | QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.save_transaction)
        button_box.rejected.connect(self.reject)

        layout.addWidget(button_box)
        self.setLayout(layout)

    def populate_form(self):
        """Populate form with transaction data."""
        type_index = 0 if self.transaction.transaction_type == "in" else 1
        self.type_combo.setCurrentIndex(type_index)

        self.amount_input.setValue(self.transaction.amount)

        # Map category
        category_map = {
            "payment": "Paiement Client",
            "expense": "Dépense",
            "transfer": "Transfert",
            "adjustment": "Ajustement"
        }
        category_text = category_map.get(self.transaction.category, "Autre")
        index = self.category_combo.findText(category_text)
        if index >= 0:
            self.category_combo.setCurrentIndex(index)

        self.description_input.setText(self.transaction.description)
        self.transaction_date.setDate(QDate.fromString(self.transaction.transaction_date.isoformat(), Qt.ISODate))

    def save_transaction(self):
        """Save the transaction."""
        # Validate amount
        if self.amount_input.value() <= 0:
            QMessageBox.warning(self, "Validation", "Le montant doit être supérieur à 0.")
            return

        # Validate description
        if not self.description_input.text().strip():
            QMessageBox.warning(self, "Validation", "La description est requise.")
            return

        # Update transaction data
        self.transaction.transaction_type = "in" if self.type_combo.currentIndex() == 0 else "out"
        self.transaction.amount = self.amount_input.value()

        # Map category
        category_map = {
            "Paiement Client": "payment",
            "Dépense": "expense",
            "Transfert": "transfer",
            "Ajustement": "adjustment",
            "Autre": "other"
        }
        self.transaction.category = category_map.get(self.category_combo.currentText(), "other")

        self.transaction.description = self.description_input.text().strip()
        self.transaction.transaction_date = self.transaction_date.date().toPyDate()

        self.accept()

    def get_transaction(self):
        """Get the transaction data."""
        return self.transaction


class PaymentsCashboxSection(QWidget):
    """Main payments and cashbox management section."""

    def __init__(self, db_session_provider, parent=None):
        super().__init__(parent)
        self.db_session_provider = db_session_provider
        self.payment_manager = PaymentManager()
        self.cashbox_manager = CashboxManager()

        # RTL when Arabic (before UI build)
        try:
            from travelpro.core.translations import translation_manager as _tr
            if _tr._lang_code(_tr.current_language) == 'ar':
                self.setLayoutDirection(Qt.RightToLeft)
            else:
                self.setLayoutDirection(Qt.LeftToRight)
        except Exception:
            pass

        self.initUI()
        self.load_data()

    def initUI(self):
        """Initialize the payments and cashbox UI."""
        try:
            from travelpro.core.translations import translation_manager as tr
        except Exception:
            tr = type('T', (), {'get_payment': staticmethod(lambda k, d=None: d or k)})()

        main_layout = QVBoxLayout()

        # Title
        title_label = QLabel(tr.get_payment('title', 'Gestion des Paiements et Caisse'))
        title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        main_layout.addWidget(title_label)

        # Quick stats panel
        self.create_stats_panel(main_layout)

        # Main content with tabs
        self.tab_widget = QTabWidget()

        # Payments tab
        self.payments_tab = self.create_payments_tab()
        self.tab_widget.addTab(self.payments_tab, tr.get_payment('tab_payments', 'Paiements'))

        # Cashbox tab
        self.cashbox_tab = self.create_cashbox_tab()
        self.tab_widget.addTab(self.cashbox_tab, tr.get_payment('tab_cashbox', 'Caisse'))

        # Reports tab
        self.reports_tab = self.create_reports_tab()
        self.tab_widget.addTab(self.reports_tab, tr.get_payment('tab_reports', 'Rapports'))

        main_layout.addWidget(self.tab_widget)
        self.setLayout(main_layout)

    def refresh_texts(self, lang_code: str):
        try:
            from travelpro.core.translations import translation_manager as tr
            # Update tab titles and any visible labels
            try:
                idx = self.tab_widget.indexOf(self.payments_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_payment('tab_payments', 'Paiements'))
                idx = self.tab_widget.indexOf(self.cashbox_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_payment('tab_cashbox', 'Caisse'))
                idx = self.tab_widget.indexOf(self.reports_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_payment('tab_reports', 'Rapports'))
            except Exception:
                pass
            for w in self.findChildren(QLabel):
                if w.text() and ('Paiements' in w.text() or 'المدفوعات' in w.text() or 'Payments' in w.text()):
                    w.setText(tr.get_payment('title', 'Gestion des Paiements et Caisse'))
                    break
            # Update payments table headers
            try:
                self.payments_table.setHorizontalHeaderLabels([
                    tr.get_payment('date', 'Date'), tr.get_payment('amount', 'Montant'), tr.get_payment('method', 'Méthode'), tr.get_payment('reference', 'Référence'), tr.get_payment('status', 'Statut'), tr.get_payment('notes', 'Notes'), tr.get_payment('actions', 'Actions')
                ])
            except Exception:
                pass
            # Update cashbox table headers
            try:
                self.cashbox_table.setHorizontalHeaderLabels([
                    tr.get_payment('date', 'Date'), tr.get_payment('type', 'Type'), tr.get_payment('amount', 'Montant'), tr.get_payment('category', 'Catégorie'), tr.get_payment('description', 'Description'), tr.get_payment('actions', 'Actions')
                ])
            except Exception:
                pass
            # Update filter labels and placeholders
            try:
                self.payment_search.setPlaceholderText(tr.get_payment('search_ph', 'Rechercher par référence, montant...'))
            except Exception:
                pass
            try:
                # Rebuild method filter preserving index
                idx_before = self.payment_method_filter.currentIndex()
                self.payment_method_filter.blockSignals(True)
                self.payment_method_filter.clear()
                self.payment_method_filter.addItems([
                    tr.get_payment('all_methods', 'Toutes Méthodes'), tr.get_payment('cash', 'Espèces'), tr.get_payment('card', 'Carte Bancaire'), tr.get_payment('transfer', 'Virement'), tr.get_payment('check', 'Chèque'), tr.get_payment('other', 'Autre')
                ])
                self.payment_method_filter.setCurrentIndex(idx_before if 0 <= idx_before < self.payment_method_filter.count() else 0)
                self.payment_method_filter.blockSignals(False)
            except Exception:
                pass
            try:
                # Rebuild status filter preserving index
                idx_before = self.payment_status_filter.currentIndex()
                self.payment_status_filter.blockSignals(True)
                self.payment_status_filter.clear()
                self.payment_status_filter.addItems([
                    tr.get_payment('all_status', 'Tous Statuts'), tr.get_payment('pending', 'En Attente'), tr.get_payment('completed', 'Complété'), tr.get_payment('failed', 'Échoué'), tr.get_payment('cancelled', 'Annulé')
                ])
                self.payment_status_filter.setCurrentIndex(idx_before if 0 <= idx_before < self.payment_status_filter.count() else 0)
                self.payment_status_filter.blockSignals(False)
            except Exception:
                pass
        except Exception:
            pass

    def create_stats_panel(self, parent_layout):
        """Create quick statistics panel."""
        stats_frame = QFrame()
        stats_frame.setFrameStyle(QFrame.StyledPanel)
        stats_layout = QHBoxLayout()

        # Cash balance card
        self.cash_balance_card = self.create_stat_card(
            "Solde Caisse", "0.00 €", "#27ae60", "mdi.cash"
        )

        # Today's payments card
        self.today_payments_card = self.create_stat_card(
            "Paiements Aujourd'hui", "0.00 €", "#3498db", "mdi.credit-card"
        )

        # Pending payments card
        self.pending_payments_card = self.create_stat_card(
            "Paiements en Attente", "0", "#f39c12", "mdi.clock"
        )

        # Monthly total card
        self.monthly_total_card = self.create_stat_card(
            "Total du Mois", "0.00 €", "#9b59b6", "mdi.chart-line"
        )

        stats_layout.addWidget(self.cash_balance_card)
        stats_layout.addWidget(self.today_payments_card)
        stats_layout.addWidget(self.pending_payments_card)
        stats_layout.addWidget(self.monthly_total_card)

        stats_frame.setLayout(stats_layout)
        parent_layout.addWidget(stats_frame)

    def create_stat_card(self, title, value, color, icon):
        """Create a statistics card."""
        card = QFrame()
        card.setFrameStyle(QFrame.StyledPanel)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 15px;
                margin: 5px;
            }}
        """)

        layout = QVBoxLayout()

        # Icon and title
        header_layout = QHBoxLayout()

        icon_label = QLabel()
        icon_label.setPixmap(qta.icon(icon, color=color).pixmap(24, 24))

        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")

        header_layout.addWidget(icon_label)
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Value
        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 20px;")
        value_label.setObjectName(f"{title.lower().replace(' ', '_')}_value")

        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        layout.addStretch()

        card.setLayout(layout)
        return card

    def create_payments_tab(self):
        """Create payments management tab."""
        tab = QWidget()
        layout = QVBoxLayout()

        # Control panel
        control_layout = QHBoxLayout()

        new_payment_btn = QPushButton(tr.get_payment('new_payment', 'Nouveau Paiement'))
        new_payment_btn.setIcon(qta.icon("mdi.plus"))
        new_payment_btn.clicked.connect(self.create_new_payment)
        new_payment_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        export_payments_btn = QPushButton(tr.get_payment('export', 'Exporter'))
        export_payments_btn.setIcon(qta.icon("mdi.export"))
        export_payments_btn.clicked.connect(self.export_payments)

        refresh_payments_btn = QPushButton(tr.get_payment('refresh', 'Actualiser'))
        refresh_payments_btn.setIcon(qta.icon("mdi.refresh"))
        refresh_payments_btn.clicked.connect(self.load_payments)

        control_layout.addWidget(new_payment_btn)
        control_layout.addWidget(export_payments_btn)
        control_layout.addWidget(refresh_payments_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        # Search and filter
        filter_layout = QHBoxLayout()

        self.payment_search = QLineEdit()
        self.payment_search.setPlaceholderText(tr.get_payment('search_ph', 'Rechercher par référence, montant...'))
        self.payment_search.textChanged.connect(self.filter_payments)

        self.payment_method_filter = QComboBox()
        self.payment_method_filter.addItems([
            tr.get_payment('all_methods', 'Toutes Méthodes'), tr.get_payment('cash', 'Espèces'), tr.get_payment('card', 'Carte Bancaire'), tr.get_payment('transfer', 'Virement'), tr.get_payment('check', 'Chèque'), tr.get_payment('other', 'Autre')
        ])
        self.payment_method_filter.currentTextChanged.connect(self.filter_payments)

        self.payment_status_filter = QComboBox()
        self.payment_status_filter.addItems([
            tr.get_payment('all_status', 'Tous Statuts'), tr.get_payment('pending', 'En Attente'), tr.get_payment('completed', 'Complété'), tr.get_payment('failed', 'Échoué'), tr.get_payment('cancelled', 'Annulé')
        ])
        self.payment_status_filter.currentTextChanged.connect(self.filter_payments)

        filter_layout.addWidget(QLabel(tr.get_payment('search', 'Recherche:')))
        filter_layout.addWidget(self.payment_search)
        filter_layout.addWidget(QLabel(tr.get_payment('method', 'Méthode:')))
        filter_layout.addWidget(self.payment_method_filter)
        filter_layout.addWidget(QLabel(tr.get_payment('status', 'Statut:')))
        filter_layout.addWidget(self.payment_status_filter)
        filter_layout.addStretch()

        layout.addLayout(filter_layout)

        # Payments table
        self.payments_table = QTableWidget()
        self.payments_table.setColumnCount(7)
        self.payments_table.setHorizontalHeaderLabels([
            tr.get_payment('date', 'Date'), tr.get_payment('amount', 'Montant'), tr.get_payment('method', 'Méthode'), tr.get_payment('reference', 'Référence'), tr.get_payment('status', 'Statut'), tr.get_payment('notes', 'Notes'), tr.get_payment('actions', 'Actions')
        ])
        self.payments_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.payments_table.setAlternatingRowColors(True)
        self.payments_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.payments_table)

        tab.setLayout(layout)
        return tab

    def create_cashbox_tab(self):
        """Create cashbox management tab."""
        tab = QWidget()
        layout = QVBoxLayout()

        # Cashbox header with balance
        header_layout = QHBoxLayout()

        try:
            from travelpro.core.translations import translation_manager as tr
            balance_label = QLabel(tr.get_payment('balance', 'Solde Actuel') + ":")
        except Exception:
            balance_label = QLabel("Solde Actuel:")
        balance_label.setStyleSheet("font-size: 16px; font-weight: bold;")

        self.balance_value_label = QLabel("0.00 €")
        self.balance_value_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        """)

        header_layout.addWidget(balance_label)
        header_layout.addWidget(self.balance_value_label)
        header_layout.addStretch()

        layout.addLayout(header_layout)

        # Control panel
        control_layout = QHBoxLayout()

        try:
            from travelpro.core.translations import translation_manager as tr
            new_transaction_btn = QPushButton(tr.get_payment('new_transaction', 'Nouvelle Transaction'))
        except Exception:
            new_transaction_btn = QPushButton("Nouvelle Transaction")
        new_transaction_btn.setIcon(qta.icon("mdi.plus"))
        new_transaction_btn.clicked.connect(self.create_new_transaction)
        new_transaction_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

        try:
            from travelpro.core.translations import translation_manager as tr
            cash_in_btn = QPushButton(tr.get_payment('cash_in', "Entrée d'Espèces"))
        except Exception:
            cash_in_btn = QPushButton("Entrée d'Espèces")
        cash_in_btn.setIcon(qta.icon("mdi.cash-plus"))
        cash_in_btn.clicked.connect(self.quick_cash_in)

        try:
            from travelpro.core.translations import translation_manager as tr
            cash_out_btn = QPushButton(tr.get_payment('cash_out', "Sortie d'Espèces"))
        except Exception:
            cash_out_btn = QPushButton("Sortie d'Espèces")
        cash_out_btn.setIcon(qta.icon("mdi.cash-minus"))
        cash_out_btn.clicked.connect(self.quick_cash_out)

        try:
            from travelpro.core.translations import translation_manager as tr
            export_cashbox_btn = QPushButton(tr.get_main('export', 'Exporter'))
        except Exception:
            export_cashbox_btn = QPushButton("Exporter")
        export_cashbox_btn.setIcon(qta.icon("mdi.export"))
        export_cashbox_btn.clicked.connect(self.export_cashbox)

        control_layout.addWidget(new_transaction_btn)
        control_layout.addWidget(cash_in_btn)
        control_layout.addWidget(cash_out_btn)
        control_layout.addWidget(export_cashbox_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        # Transactions table
        self.cashbox_table = QTableWidget()
        self.cashbox_table.setColumnCount(6)
        try:
            from travelpro.core.translations import translation_manager as tr
            self.cashbox_table.setHorizontalHeaderLabels([
                tr.get_payment('date', 'Date'),
                tr.get_payment('type', 'Type'),
                tr.get_payment('amount', 'Montant'),
                tr.get_payment('category', 'Catégorie'),
                tr.get_payment('description', 'Description'),
                tr.get_payment('actions', 'Actions')
            ])
        except Exception:
            self.cashbox_table.setHorizontalHeaderLabels([
                "Date", "Type", "Montant", "Catégorie", "Description", "Actions"
            ])
        self.cashbox_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.cashbox_table.setAlternatingRowColors(True)
        self.cashbox_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.cashbox_table)

        tab.setLayout(layout)
        return tab

    def create_reports_tab(self):
        """Create financial reports tab."""
        tab = QWidget()
        layout = QVBoxLayout()

        # Report controls
        controls_layout = QHBoxLayout()

        self.report_start_date = QDateEdit()
        self.report_start_date.setCalendarPopup(True)
        self.report_start_date.setDate(QDate.currentDate().addDays(-30))

        self.report_end_date = QDateEdit()
        self.report_end_date.setCalendarPopup(True)
        self.report_end_date.setDate(QDate.currentDate())

        try:
            from travelpro.core.translations import translation_manager as tr
            generate_report_btn = QPushButton(tr.get_reports('generate', 'Générer Rapport'))
        except Exception:
            generate_report_btn = QPushButton("Générer Rapport")
        generate_report_btn.setIcon(qta.icon("mdi.chart-bar"))
        generate_report_btn.clicked.connect(self.generate_financial_report)

        try:
            from travelpro.core.translations import translation_manager as tr
            controls_layout.addWidget(QLabel(tr.get_reports('from', 'Du:')))
        except Exception:
            controls_layout.addWidget(QLabel("Du:"))
        controls_layout.addWidget(self.report_start_date)
        try:
            from travelpro.core.translations import translation_manager as tr
            controls_layout.addWidget(QLabel(tr.get_reports('to', 'Au:')))
        except Exception:
            controls_layout.addWidget(QLabel("Au:"))
        controls_layout.addWidget(self.report_end_date)
        controls_layout.addWidget(generate_report_btn)
        controls_layout.addStretch()

        layout.addLayout(controls_layout)

        # Report display
        self.report_display = QTextEdit()
        self.report_display.setReadOnly(True)
        self.report_display.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                font-family: 'Courier New', monospace;
                font-size: 12px;
            }
        """)

        layout.addWidget(self.report_display)

        tab.setLayout(layout)
        return tab

    def load_data(self):
        """Load all data and update displays."""
        self.load_payments()
        self.load_cashbox()
        self.update_statistics()

    def load_payments(self):
        """Load and display payments."""
        self.payment_manager.load_payments()
        self.populate_payments_table()

    def load_cashbox(self):
        """Load and display cashbox transactions."""
        self.cashbox_manager.load_transactions()
        self.populate_cashbox_table()
        self.update_cashbox_balance()

    def update_statistics(self):
        """Update statistics cards."""
        # Cash balance
        balance = self.cashbox_manager.get_balance()
        self.update_stat_card(self.cash_balance_card, f"{balance:.2f} €")

        # Today's payments
        today = datetime.now().date()
        today_payments = [p for p in self.payment_manager.payments
                         if p.payment_date == today and p.status == "completed"]
        today_total = sum(p.amount for p in today_payments)
        self.update_stat_card(self.today_payments_card, f"{today_total:.2f} €")

        # Pending payments
        pending_count = len([p for p in self.payment_manager.payments if p.status == "pending"])
        self.update_stat_card(self.pending_payments_card, str(pending_count))

        # Monthly total
        current_month = datetime.now().replace(day=1).date()
        monthly_payments = [p for p in self.payment_manager.payments
                           if p.payment_date >= current_month and p.status == "completed"]
        monthly_total = sum(p.amount for p in monthly_payments)
        self.update_stat_card(self.monthly_total_card, f"{monthly_total:.2f} €")

    def update_stat_card(self, card, value):
        """Update a statistics card value."""
        for child in card.findChildren(QLabel):
            if "value" in child.objectName():
                child.setText(value)
                break

    def update_cashbox_balance(self):
        """Update cashbox balance display."""
        balance = self.cashbox_manager.get_balance()
        self.balance_value_label.setText(f"{balance:.2f} €")

        # Change color based on balance
        if balance < 0:
            color = "#e74c3c"  # Red for negative
        elif balance < 100:
            color = "#f39c12"  # Orange for low
        else:
            color = "#27ae60"  # Green for good

        self.balance_value_label.setStyleSheet(f"""
            font-size: 24px;
            font-weight: bold;
            color: {color};
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        """)

    def create_new_payment(self):
        """Create a new payment."""
        dialog = PaymentDialog(self.db_session_provider, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            payment = dialog.get_payment()
            self.payment_manager.create_payment(payment)

            # Add to cashbox if it's a cash payment
            if dialog.should_add_to_cashbox() and payment.payment_method == "cash":
                transaction = CashTransaction(
                    transaction_type="in",
                    amount=payment.amount,
                    description=f"Paiement client - {payment.reference}",
                    category="payment",
                    payment_id=payment.id
                )
                self.cashbox_manager.add_transaction(transaction)

            self.load_data()
            QMessageBox.information(self, "Succès", "Paiement enregistré avec succès!")

    def filter_payments(self):
        """Filter payments based on search criteria."""
        search_text = self.payment_search.text().lower()
        method_filter = self.payment_method_filter.currentText()
        status_filter = self.payment_status_filter.currentText()

        filtered_payments = []

        for payment in self.payment_manager.payments:
            # Search filter
            if search_text and search_text not in payment.reference.lower() and search_text not in str(payment.amount):
                continue

            # Method filter
            if method_filter != "Toutes Méthodes":
                method_map = {
                    "Espèces": "cash",
                    "Carte Bancaire": "card",
                    "Virement": "transfer",
                    "Chèque": "check",
                    "Autre": "other"
                }
                if payment.payment_method != method_map.get(method_filter):
                    continue

            # Status filter
            if status_filter != "Tous Statuts":
                status_map = {
                    "En Attente": "pending",
                    "Complété": "completed",
                    "Échoué": "failed",
                    "Annulé": "cancelled"
                }
                if payment.status != status_map.get(status_filter):
                    continue

            filtered_payments.append(payment)

        # Update table with filtered results
        self.populate_payments_table_with_data(filtered_payments)

    def populate_payments_table(self):
        """Populate payments table with all payments."""
        self.populate_payments_table_with_data(self.payment_manager.payments)

    def populate_payments_table_with_data(self, payments):
        """Populate payments table with given data."""
        self.payments_table.setRowCount(len(payments))

        for row, payment in enumerate(payments):
            # Date
            date_str = payment.payment_date.strftime("%d/%m/%Y") if payment.payment_date else ""
            self.payments_table.setItem(row, 0, QTableWidgetItem(date_str))

            # Amount
            self.payments_table.setItem(row, 1, QTableWidgetItem(f"{payment.amount:.2f} €"))

            # Method
            method_text = self.get_payment_method_text(payment.payment_method)
            self.payments_table.setItem(row, 2, QTableWidgetItem(method_text))

            # Reference
            self.payments_table.setItem(row, 3, QTableWidgetItem(payment.reference))

            # Status
            status_item = QTableWidgetItem(self.get_status_text(payment.status))
            status_item.setBackground(self.get_status_color(payment.status))
            self.payments_table.setItem(row, 4, status_item)

            # Notes
            self.payments_table.setItem(row, 5, QTableWidgetItem(payment.notes[:50] + "..." if len(payment.notes) > 50 else payment.notes))

            # Actions
            actions_widget = self.create_payment_actions_widget(payment)
            self.payments_table.setCellWidget(row, 6, actions_widget)

    def get_payment_method_text(self, method):
        """Get payment method display text."""
        method_map = {
            "cash": "Espèces",
            "card": "Carte Bancaire",
            "transfer": "Virement",
            "check": "Chèque",
            "other": "Autre"
        }
        return method_map.get(method, method)

    def get_status_text(self, status):
        """Get status display text."""
        status_map = {
            "pending": "En Attente",
            "completed": "Complété",
            "failed": "Échoué",
            "cancelled": "Annulé"
        }
        return status_map.get(status, status)

    def get_status_color(self, status):
        """Get status color."""
        from PyQt5.QtGui import QColor

        color_map = {
            "pending": QColor("#f39c12"),
            "completed": QColor("#27ae60"),
            "failed": QColor("#e74c3c"),
            "cancelled": QColor("#95a5a6")
        }
        return color_map.get(status, QColor("#bdc3c7"))

    def create_payment_actions_widget(self, payment):
        """Create actions widget for payment row."""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)

        # Edit button
        edit_btn = QPushButton()
        edit_btn.setIcon(qta.icon("mdi.pencil"))
        edit_btn.setToolTip("Modifier")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_payment(payment))

        # Delete button
        delete_btn = QPushButton()
        delete_btn.setIcon(qta.icon("mdi.delete"))
        delete_btn.setToolTip("Supprimer")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.delete_payment(payment))

        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        return widget

    def edit_payment(self, payment):
        """Edit an existing payment."""
        dialog = PaymentDialog(self.db_session_provider, payment, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            updated_payment = dialog.get_payment()
            self.payment_manager.update_payment(updated_payment)
            self.load_data()
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_payment('success', 'Succès')
                _text = tr.get_payment('payment_updated', 'Paiement mis à jour avec succès!')
            except Exception:
                _title = 'Succès'
                _text = 'Paiement mis à jour avec succès!'
            QMessageBox.information(self, _title, _text)

    def delete_payment(self, payment):
        """Delete a payment."""
        try:
            from travelpro.core.translations import translation_manager as tr
            _q_title = tr.get_system('confirm_action', 'Confirmer')
            _q_text = tr.get_system('confirm_delete', "Êtes-vous sûr de vouloir supprimer cet élément ?")
        except Exception:
            _q_title = 'Confirmer'
            _q_text = "Êtes-vous sûr de vouloir supprimer cet élément ?"
        reply = QMessageBox.question(
            self,
            _q_title,
            _q_text,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.payment_manager.delete_payment(payment.id)
            self.load_data()
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_payment('success', 'Succès')
                _text = tr.get_payment('payment_deleted', 'Paiement supprimé avec succès!')
            except Exception:
                _title = 'Succès'
                _text = 'Paiement supprimé avec succès!'
            QMessageBox.information(self, _title, _text)

    def export_payments(self):
        """Export payments to CSV file."""
        from PyQt5.QtWidgets import QFileDialog
        import csv

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les Paiements",
            f"paiements_{datetime.now().strftime('%Y%m%d')}.csv",
            "CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # Header
                    writer.writerow([
                        'Date', 'Montant', 'Méthode', 'Référence', 'Statut', 'Notes'
                    ])

                    # Data
                    for payment in self.payment_manager.payments:
                        writer.writerow([
                            payment.payment_date.strftime("%d/%m/%Y") if payment.payment_date else "",
                            payment.amount,
                            self.get_payment_method_text(payment.payment_method),
                            payment.reference,
                            self.get_status_text(payment.status),
                            payment.notes
                        ])

                QMessageBox.information(self, "Export", f"Paiements exportés vers:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")

    def create_new_transaction(self):
        """Create a new cash transaction."""
        dialog = CashTransactionDialog(parent=self)
        if dialog.exec_() == QDialog.Accepted:
            transaction = dialog.get_transaction()
            self.cashbox_manager.add_transaction(transaction)
            self.load_data()
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_payment('success', 'Succès')
                _text = tr.get_payment('transaction_added', 'Transaction ajoutée avec succès!')
            except Exception:
                _title = 'Succès'
                _text = 'Transaction ajoutée avec succès!'
            QMessageBox.information(self, _title, _text)

    def quick_cash_in(self):
        """Quick cash in dialog."""
        from PyQt5.QtWidgets import QInputDialog

        try:
            from travelpro.core.translations import translation_manager as tr
            title_in = tr.get_payment('cash_in', "Entrée d'Espèces")
            label_amount_in = tr.get_payment('amount_to_add', 'Montant à ajouter:')
        except Exception:
            title_in = "Entrée d'Espèces"
            label_amount_in = 'Montant à ajouter:'

        amount, ok = QInputDialog.getDouble(
            self,
            title_in,
            label_amount_in,
            0.0, 0.0, 999999.99, 2
        )

        if ok and amount > 0:
            try:
                from travelpro.core.translations import translation_manager as tr
                title_desc_in = tr.get_payment('description', 'Description')
                label_desc_in = tr.get_payment('cash_in_description', "Description de l'entrée:")
            except Exception:
                title_desc_in = 'Description'
                label_desc_in = "Description de l'entrée:"

            description, ok2 = QInputDialog.getText(
                self,
                title_desc_in,
                label_desc_in
            )

            if ok2:
                try:
                    from travelpro.core.translations import translation_manager as tr
                    default_desc_in = tr.get_payment('cash_in', "Entrée d'espèces")
                except Exception:
                    default_desc_in = "Entrée d'espèces"

                transaction = CashTransaction(
                    transaction_type="in",
                    amount=amount,
                    description=description or default_desc_in,
                    category="other"
                )
                self.cashbox_manager.add_transaction(transaction)
                self.load_data()
                try:
                    from travelpro.core.translations import translation_manager as tr
                    title = tr.get_payment('success', 'Succès')
                    text = tr.get_payment('cash_in_success', f"Entrée de {amount:.2f} € ajoutée!")
                except Exception:
                    title = "Succès"
                    text = f"Entrée de {amount:.2f} € ajoutée!"
                QMessageBox.information(self, title, text)

    def quick_cash_out(self):
        """Quick cash out dialog."""
        from PyQt5.QtWidgets import QInputDialog

        try:
            from travelpro.core.translations import translation_manager as tr
            title_out = tr.get_payment('cash_out', "Sortie d'Espèces")
            label_amount = tr.get_payment('amount_to_withdraw', 'Montant à retirer:')
        except Exception:
            title_out = "Sortie d'Espèces"
            label_amount = 'Montant à retirer:'

        amount, ok = QInputDialog.getDouble(
            self,
            title_out,
            label_amount,
            0.0, 0.0, 999999.99, 2
        )

        if ok and amount > 0:
            try:
                from travelpro.core.translations import translation_manager as tr
                title_desc = tr.get_payment('description', 'Description')
                label_desc = tr.get_payment('cash_out_description', 'Description de la sortie:')
            except Exception:
                title_desc = 'Description'
                label_desc = 'Description de la sortie:'

            description, ok2 = QInputDialog.getText(
                self,
                title_desc,
                label_desc
            )

            if ok2:
                transaction = CashTransaction(
                    transaction_type="out",
                    amount=amount,
                    description=description or "Sortie d'espèces",
                    category="other"
                )
                self.cashbox_manager.add_transaction(transaction)
                self.load_data()
                try:
                    from travelpro.core.translations import translation_manager as tr
                    title = tr.get_payment('success', 'Succès')
                    text = tr.get_payment('cash_out_success', f"Sortie de {amount:.2f} € enregistrée!")
                except Exception:
                    title = "Succès"
                    text = f"Sortie de {amount:.2f} € enregistrée!"
                QMessageBox.information(self, title, text)

    def export_cashbox(self):
        """Export cashbox transactions to CSV file."""
        from PyQt5.QtWidgets import QFileDialog
        import csv

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "Exporter les Transactions",
            f"caisse_{datetime.now().strftime('%Y%m%d')}.csv",
            "CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)

                    # Header
                    writer.writerow([
                        'Date', 'Type', 'Montant', 'Catégorie', 'Description'
                    ])

                    # Data
                    for transaction in self.cashbox_manager.transactions:
                        writer.writerow([
                            transaction.transaction_date.strftime("%d/%m/%Y") if transaction.transaction_date else "",
                            "Entrée" if transaction.transaction_type == "in" else "Sortie",
                            transaction.amount,
                            transaction.category,
                            transaction.description
                        ])

                QMessageBox.information(self, "Export", f"Transactions exportées vers:\n{file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Erreur", f"Erreur lors de l'export: {str(e)}")

    def populate_cashbox_table(self):
        """Populate cashbox table."""
        transactions = self.cashbox_manager.transactions
        self.cashbox_table.setRowCount(len(transactions))

        for row, transaction in enumerate(transactions):
            # Date
            date_str = transaction.transaction_date.strftime("%d/%m/%Y") if transaction.transaction_date else ""
            self.cashbox_table.setItem(row, 0, QTableWidgetItem(date_str))

            # Type
            type_text = "Entrée" if transaction.transaction_type == "in" else "Sortie"
            self.cashbox_table.setItem(row, 1, QTableWidgetItem(type_text))

            # Amount
            amount_text = f"+{transaction.amount:.2f} €" if transaction.transaction_type == "in" else f"-{transaction.amount:.2f} €"
            self.cashbox_table.setItem(row, 2, QTableWidgetItem(amount_text))

            # Category
            self.cashbox_table.setItem(row, 3, QTableWidgetItem(transaction.category))

            # Description
            self.cashbox_table.setItem(row, 4, QTableWidgetItem(transaction.description))

            # Actions
            actions_widget = self.create_transaction_actions_widget(transaction)
            self.cashbox_table.setCellWidget(row, 5, actions_widget)

    def create_transaction_actions_widget(self, transaction):
        """Create actions widget for transaction row."""
        widget = QWidget()
        layout = QHBoxLayout()
        layout.setContentsMargins(2, 2, 2, 2)

        # Edit button
        edit_btn = QPushButton()
        edit_btn.setIcon(qta.icon("mdi.pencil"))
        edit_btn.setToolTip("Modifier")
        edit_btn.setFixedSize(30, 30)
        edit_btn.clicked.connect(lambda: self.edit_transaction(transaction))

        # Delete button
        delete_btn = QPushButton()
        delete_btn.setIcon(qta.icon("mdi.delete"))
        delete_btn.setToolTip("Supprimer")
        delete_btn.setFixedSize(30, 30)
        delete_btn.clicked.connect(lambda: self.delete_transaction(transaction))

        layout.addWidget(edit_btn)
        layout.addWidget(delete_btn)

        widget.setLayout(layout)
        return widget

    def edit_transaction(self, transaction):
        """Edit a transaction."""
        dialog = CashTransactionDialog(transaction, parent=self)
        if dialog.exec_() == QDialog.Accepted:
            updated_transaction = dialog.get_transaction()
            # Update in manager (would need update method)
            self.load_data()
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_payment('success', 'Succès')
                _text = tr.get_payment('transaction_updated', 'Transaction mise à jour!')
            except Exception:
                _title = 'Succès'
                _text = 'Transaction mise à jour!'
            QMessageBox.information(self, _title, _text)

    def delete_transaction(self, transaction):
        """Delete a transaction."""
        try:
            from travelpro.core.translations import translation_manager as tr
            _q_title = tr.get_system('confirm_action', 'Confirmer')
            _q_text = tr.get_system('confirm_delete', "Êtes-vous sûr de vouloir supprimer cet élément ?")
        except Exception:
            _q_title = 'Confirmer'
            _q_text = "Êtes-vous sûr de vouloir supprimer cet élément ?"
        reply = QMessageBox.question(
            self,
            _q_title,
            _q_text,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from manager (would need delete method)
            self.load_data()
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_payment('success', 'Succès')
                _text = tr.get_payment('transaction_deleted', 'Transaction supprimée!')
            except Exception:
                _title = 'Succès'
                _text = 'Transaction supprimée!'
            QMessageBox.information(self, _title, _text)

    def generate_financial_report(self):
        """Generate financial report."""
        start_date = self.report_start_date.date().toPyDate()
        end_date = self.report_end_date.date().toPyDate()

        # Filter payments and transactions by date range
        filtered_payments = self.payment_manager.get_payments_by_date_range(start_date, end_date)
        filtered_transactions = [t for t in self.cashbox_manager.transactions
                               if start_date <= t.transaction_date <= end_date]

        # Generate report
        report = f"RAPPORT FINANCIER\n"
        report += f"Période: {start_date.strftime('%d/%m/%Y')} - {end_date.strftime('%d/%m/%Y')}\n"
        report += f"Généré le: {datetime.now().strftime('%d/%m/%Y %H:%M')}\n"
        report += "=" * 60 + "\n\n"

        # Payments summary
        total_payments = sum(p.amount for p in filtered_payments if p.status == "completed")
        report += f"PAIEMENTS REÇUS:\n"
        report += f"Total: {total_payments:.2f} €\n"
        report += f"Nombre: {len(filtered_payments)}\n\n"

        # Cash transactions summary
        cash_in = sum(t.amount for t in filtered_transactions if t.transaction_type == "in")
        cash_out = sum(t.amount for t in filtered_transactions if t.transaction_type == "out")
        net_cash = cash_in - cash_out

        report += f"MOUVEMENTS DE CAISSE:\n"
        report += f"Entrées: {cash_in:.2f} €\n"
        report += f"Sorties: {cash_out:.2f} €\n"
        report += f"Net: {net_cash:.2f} €\n\n"

        # Current balance
        current_balance = self.cashbox_manager.get_balance()
        report += f"SOLDE ACTUEL DE CAISSE: {current_balance:.2f} €\n"

        self.report_display.setPlainText(report)
