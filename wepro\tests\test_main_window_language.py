import unittest, os, sys
from PyQt5.QtWidgets import QApplication

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class TestMainWindowLanguage(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls._app = QApplication.instance() or QApplication([])

    def test_main_window_smoke_ar_en_fr(self):
        from travelpro.ui.main_window import MainWindow
        from travelpro.core.translations import translation_manager as tr

        class DummyDBQuery:
            def all(self):
                return []
        class DummyDB:
            def query(self, *args, **kwargs):
                return DummyDBQuery()
            def close(self):
                pass
        class _DummyCtx:
            def __enter__(self):
                return DummyDB()
            def __exit__(self, exc_type, exc, tb):
                return False
        def dummy_db_provider():
            return _DummyCtx()

        for code in ['ar','en','fr']:
            tr.set_language(code)
            w = MainWindow(db_session_provider=dummy_db_provider)
            # ensure window title is a non-empty string
            self.assertTrue(isinstance(w.windowTitle(), str))
            w.deleteLater()

    def test_dynamic_language_change(self):
        from travelpro.ui.main_window import MainWindow
        from travelpro.core.translations import translation_manager as tr

        class DummyDBQuery:
            def all(self):
                return []
        class DummyDB:
            def query(self, *args, **kwargs):
                return DummyDBQuery()
            def close(self):
                pass
        class _DummyCtx:
            def __enter__(self):
                return DummyDB()
            def __exit__(self, exc_type, exc, tb):
                return False
        def dummy_db_provider():
            return _DummyCtx()

        tr.set_language('fr')
        w = MainWindow(db_session_provider=dummy_db_provider)
        # simulate changing to English and refresh
        tr.set_language('en')
        w.refresh_texts('en')
        # title should now be something (cannot assert exact)
        self.assertIsInstance(w.windowTitle(), str)
        w.deleteLater()

if __name__ == '__main__':
    unittest.main()

