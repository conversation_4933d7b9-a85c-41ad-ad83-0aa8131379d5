

import sys
import logging
from travelpro.core.config import Config
from travelpro.core.database import Database
from travelpro.core.auth import AuthManager
from travelpro.ui.main_window import run_app

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def initialize_app():
    """Initialise l'application et ses composants."""
    logger.info("Initialisation de l'application TravelPro")
    
    # Chargement de la configuration
    config = Config()
    logger.info("Configuration chargée")
    
    # Initialisation de la base de données
    db = Database()
    db.init_db()
    logger.info("Base de données initialisée")
    
    # Initialisation du gestionnaire d'authentification
    auth_manager = AuthManager()
    auth_manager.create_admin_if_not_exists()
    logger.info("Gestionnaire d'authentification initialisé")
    
    return True

def main():
    """Fonction principale de l'application."""
    try:
        # Initialisation de l'application
        if not initialize_app():
            logger.error("Échec de l'initialisation de l'application")
            sys.exit(1)
        
        # Lancement de l'interface utilisateur
        logger.info("Lancement de l'interface utilisateur")
        run_app()
        
    except Exception as e:
        logger.error(f"Erreur lors du démarrage de l'application: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
