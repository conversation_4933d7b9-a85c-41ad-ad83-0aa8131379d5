import unittest
import os, sys

# Ensure wepro is on sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class TestResourcePaths(unittest.TestCase):
    def test_images_exist_or_fail_gracefully(self):
        from travelpro.ui.login_screen import resource_path
        for name in ["0.png", "00.png", "email_icon.svg", "password_icon.svg"]:
            path = resource_path(name)
            self.assertTrue(isinstance(path, str))

if __name__ == '__main__':
    unittest.main()

