import unittest
import os, sys
# Ensure wepro in sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from travelpro.core.config import Config

class TestConfigPersistence(unittest.TestCase):
    def test_language_and_theme_persistence(self):
        cfg = Config()
        cfg.set('language', 'en')
        cfg.set('ui.theme_key', 'modern_blue')
        self.assertEqual(cfg.get('language'), 'en')
        self.assertEqual(cfg.get('ui.theme_key'), 'modern_blue')

if __name__ == '__main__':
    unittest.main()

