from PyQt5.QtWidgets import Q<PERSON>idget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QMessageBox, QDesktopWidget, QCheckBox
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import Qt, pyqtSignal

# Import authentication logic and database session provider
from logic.auth import authenticate_user
from database import get_db

# Import du système de traduction
try:
    from travelpro.core.translations import translation_manager as tr
except ImportError:
    # Fallback si le système de traduction n'est pas disponible
    class FallbackTranslation:
        def get_login(self, key, default=None):
            return default or key
    tr = FallbackTranslation()


import os

def resource_path(relative: str) -> str:
    """Resolve resource paths regardless of current working directory.
    Tries common locations:
    - wepro/<relative>
    - alongside this file
    - ../resources next to this file
    - repository root
    - current working directory
    Returns the first existing path, or a best-guess under wepro.
    """
    try:
        here = os.path.dirname(os.path.abspath(__file__))
        wepro_dir = os.path.abspath(os.path.join(here, os.pardir, os.pardir))  # .../wepro
        repo_root = os.path.abspath(os.path.join(wepro_dir, os.pardir))        # repo root
        candidates = [
            os.path.join(wepro_dir, relative),                 # wepro/<relative>
            os.path.join(here, relative),                      # same folder as this file
            os.path.abspath(os.path.join(here, os.pardir, 'resources', relative)),  # travelpro/resources/
            os.path.join(repo_root, relative),                 # repository root
            os.path.join(os.getcwd(), relative),               # CWD
        ]
        for p in candidates:
            if os.path.exists(p):
                return p
        # Default to wepro/<relative>
        return os.path.join(wepro_dir, relative)
    except Exception:
        return relative

class LoginScreen(QWidget):
    # Define a signal to be emitted upon successful login
    login_success = pyqtSignal(object) # Signal can carry user object or other data

    def __init__(self, db_session_provider, parent=None):
        super().__init__(parent)
        self.db_session_provider = db_session_provider
        self.setWindowTitle(tr.get_login("login_title", "TravelPro - Connexion"))
        # Set a fixed size for the login window
        self.setFixedSize(800, 500) # Increased size for two-column layout
        self.initUI()
        # Center the window on the screen
        self.center()

    def center(self):
        # Get the geometry of the main screen
        screen = QDesktopWidget().screenGeometry()
        # Find the center point
        center_point = screen.center()
        # Move the window's top-left corner to the center point
        self.move(center_point.x() - self.width() // 2, center_point.y() - self.height() // 2)

    def initUI(self):
        main_layout = QHBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Left Panel (Promotional Content)
        left_panel = QWidget()
        left_panel.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #0A2342, stop:1 #1E4072);
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
            }
            QLabel {
                color: #FFFFFF;
            }
            QPushButton {
                background-color: #007BFF;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        left_layout = QVBoxLayout(left_panel)
        left_layout.setAlignment(Qt.AlignCenter)
        left_layout.setContentsMargins(30, 30, 30, 30)

        # Add world map background (conceptual, not actual image)
        # For a real implementation, you'd use a QGraphicsView or similar for complex backgrounds
        # For now, we'll just rely on the gradient.

        logo_label = QLabel()
        logo_path = resource_path("0.png")
        if os.path.exists(logo_path):
            logo_pixmap = QPixmap(logo_path)
            logo_pixmap = logo_pixmap.scaled(950, 450, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            logo_label.setPixmap(logo_pixmap)
        else:
            logo_label.setText("")
        logo_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(logo_label)



        left_layout.addStretch(1)

        # Adjust alignment and margins for logo and WELCOME text
        left_layout.setContentsMargins(30, 100, 30, 30) # Top margin increased to move content higher
        left_layout.setAlignment(Qt.AlignHCenter | Qt.AlignTop) # Align content to top and center horizontally

        # Right Panel (Login Form)
        right_panel = QWidget()
        right_panel.setStyleSheet("""
            QWidget {
                background-color: #FFFFFF;
                border-top-right-radius: 10px;
                border-bottom-right-radius: 10px;

            }
            QLabel {
                color: #333333;
                font-size: 14px;
            }
            QLineEdit {
                background-color: #F0F0F0;
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                color: #333333;
                padding: 10px;
                padding-left: 40px; /* Space for icon */
            }
            QPushButton {
                background-color: #007BFF;
                color: #FFFFFF;
                border: none;
                border-radius: 5px;
                padding: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QCheckBox {
                color: #333333;
            }
            QCheckBox::indicator {
                width: 15px;
                height: 15px;
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                background-color: #F0F0F0;
            }
            QCheckBox::indicator:checked {
                background-color: #007BFF;
            }
            QMessageBox {
                background-color: #FFFFFF;
                color: #333333;
            }
        """)
        right_layout = QVBoxLayout(right_panel)
        right_layout.setAlignment(Qt.AlignCenter)
        right_layout.setContentsMargins(30, 30, 30, 30)

        # Rocket Icon
        rocket_icon_label = QLabel()
        rocket_path = resource_path("00.png")
        if os.path.exists(rocket_path):
            rocket_pixmap = QPixmap(rocket_path)
            rocket_pixmap = rocket_pixmap.scaled(80, 80, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            rocket_icon_label.setPixmap(rocket_pixmap)
        rocket_icon_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(rocket_icon_label)

        title_label = QLabel("BIENVENUE")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 20px; color: #333333;")
        right_layout.addWidget(title_label)

        # Username input
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText(tr.get_login("username", "Nom d'utilisateur"))
        email_icon_path = resource_path("email_icon.svg")
        if os.path.exists(email_icon_path):
            email_icon = QIcon(email_icon_path)
            self.username_input.addAction(email_icon, QLineEdit.LeadingPosition)
        right_layout.addWidget(self.username_input)

        # Password input
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText(tr.get_login("password", "Mot de passe"))
        self.password_input.setEchoMode(QLineEdit.Password)
        password_icon_path = resource_path("password_icon.svg")
        if os.path.exists(password_icon_path):
            password_icon = QIcon(password_icon_path)
            self.password_input.addAction(password_icon, QLineEdit.LeadingPosition)
        right_layout.addWidget(self.password_input)

        # Checkbox and Forgot Password link in a horizontal layout
        checkbox_forgot_layout = QHBoxLayout()
        self.remember_me_checkbox = QCheckBox(tr.get_login("remember_me", "Se souvenir de moi"))
        checkbox_forgot_layout.addWidget(self.remember_me_checkbox)
        checkbox_forgot_layout.addStretch(1)

        forgot_text = tr.get_login("forgot_password", "Mot de passe oublié ?")
        self.forgot_password_label = QLabel(f"<a href=\"#\" style=\"color: #007BFF; text-decoration: none; font-size: 12px;\">{forgot_text}</a>")
        self.forgot_password_label.setAlignment(Qt.AlignRight)
        self.forgot_password_label.setOpenExternalLinks(False)
        self.forgot_password_label.linkActivated.connect(self.show_forgot_password_message)
        checkbox_forgot_layout.addWidget(self.forgot_password_label)
        right_layout.addLayout(checkbox_forgot_layout)

        # Login button
        self.login_button = QPushButton(tr.get_login("login", "Se connecter"))

        self.login_button.clicked.connect(self.attempt_login)
        right_layout.addWidget(self.login_button)

        self.exit_button = QPushButton("Quitter")
        self.exit_button.clicked.connect(self.close)
        right_layout.addWidget(self.exit_button)



        main_layout.addWidget(left_panel, 2)
        main_layout.addWidget(right_panel, 1)

        self.setLayout(main_layout)


        # Forgot Password link
        self.forgot_password_label = QLabel("<a href=\"#\" style=\"color: #7289DA; text-decoration: none;\">Forgot Password?</a>")
        self.forgot_password_label.setAlignment(Qt.AlignCenter)
        self.forgot_password_label.setOpenExternalLinks(False) # Prevent opening external links
        self.forgot_password_label.linkActivated.connect(self.show_forgot_password_message)




    def attempt_login(self):
        username = self.username_input.text()
        password = self.password_input.text()

        print(f"Attempting login with username: {username}")

        # Use the provided database session provider
        db = next(self.db_session_provider())
        user = authenticate_user(db, username, password)
        db.close()

        if user:
            print("Connexion réussie!")
            # Emit the signal upon successful login, potentially passing the user object
            self.login_success.emit(user)
            self.close() # Close the login window on successful login
        else:
            error_title = tr.get_login("login_failed", "Échec de la connexion")
            error_msg = tr.get_login("invalid_credentials", "Nom d'utilisateur ou mot de passe incorrect")
            QMessageBox.warning(self, error_title, error_msg)

    def show_forgot_password_message(self):
        title = tr.get_login("forgot_password", "Mot de passe oublié")
        message = tr.get_login("forgot_password_message", "Veuillez contacter votre administrateur pour réinitialiser votre mot de passe.")
        QMessageBox.information(self, title, message)