"""
Settings Section for TravelPro
Contains unified data entry and application settings
"""

from PyQt5.QtWidgets import (
    QWidget, Q<PERSON>oxLayout, QHBoxLayout, QLabel, QPushButton, QTabWidget,
    QGroupBox, QFormLayout, QLineEdit, QComboBox, QCheckBox, QSpinBox,
    QDoubleSpinBox, QTextEdit, QMessageBox, QScrollArea, QFileDialog,
    QProgressBar, QSlider
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap
import os
import json

# Import unified entry section
from .unified_entry_section import UnifiedEntrySection

# Import config system
try:
    from travelpro.core.config import Config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False

# Try to import advanced settings manager
try:
    from travelpro.settings.settings_manager import SettingsManager
    ADVANCED_SETTINGS_AVAILABLE = True
except ImportError:
    ADVANCED_SETTINGS_AVAILABLE = False

# Try to import backup manager
try:
    from travelpro.utils.backup_manager import BackupManagerWidget
    BACKUP_AVAILABLE = True
except ImportError:
    BACKUP_AVAILABLE = False

class SettingsSection(QWidget):
    """Settings and configuration interface."""

    def __init__(self, db_session_provider, parent=None):
        super().__init__(parent)
        self.db_session_provider = db_session_provider

        # Initialize config system
        if CONFIG_AVAILABLE:
            self.config = Config()
        else:
            self.config = None

        self.initUI()
        self.load_settings()

    def refresh_texts(self, lang_code: str):
        """Refresh translatable strings when language changes."""
        try:
            from travelpro.core.translations import translation_manager as tr
            # Update title
            try:
                self.title_label.setText(tr.get_settings('settings', 'الإعدادات والتكوين'))
            except Exception:
                pass
            # Update tab titles
            try:
                idx = self.tab_widget.indexOf(self.unified_entry_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_settings('unified_entry', 'إدخال بيانات موحد'))
                idx = self.tab_widget.indexOf(self.basic_settings_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_settings('basic_settings', 'الإعدادات الأساسية'))
                idx = self.tab_widget.indexOf(self.company_settings_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_settings('company_data', 'بيانات الشركة'))
                idx = self.tab_widget.indexOf(self.system_settings_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_settings('system_settings', 'إعدادات النظام'))
                idx = self.tab_widget.indexOf(self.import_export_tab)
                if idx != -1:
                    self.tab_widget.setTabText(idx, tr.get_settings('import_export', 'استيراد/تصدير'))
                if hasattr(self, 'advanced_settings_tab'):
                    idx = self.tab_widget.indexOf(self.advanced_settings_tab)
                    if idx != -1:
                        self.tab_widget.setTabText(idx, tr.get_settings('advanced_settings', 'الإعدادات المتقدمة'))
            except Exception:
                pass
        except Exception:
            pass

    def initUI(self):
        """Initialize the settings interface."""
        main_layout = QVBoxLayout()

        # Title
        from travelpro.core.translations import translation_manager as tr
        self.title_label = QLabel(tr.get_settings('settings', 'الإعدادات والتكوين'))
        self.title_label.setStyleSheet("""
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 20px;
            padding: 10px;
            background: #ecf0f1;
            border-radius: 5px;
        """)
        main_layout.addWidget(self.title_label)

        # Create tab widget for different settings
        self.tab_widget = QTabWidget()

        # Unified data entry tab
        self.unified_entry_tab = UnifiedEntrySection(db_session_provider=self.db_session_provider)
        self.tab_widget.addTab(self.unified_entry_tab, tr.get_settings('unified_entry', 'إدخال بيانات موحد'))

        # Basic settings tab
        self.basic_settings_tab = self.create_basic_settings_tab()
        self.tab_widget.addTab(self.basic_settings_tab, tr.get_settings('basic_settings', 'الإعدادات الأساسية'))

        # Company settings tab
        self.company_settings_tab = self.create_company_settings_tab()
        self.tab_widget.addTab(self.company_settings_tab, tr.get_settings('company_data', 'بيانات الشركة'))

        # System settings tab
        self.system_settings_tab = self.create_system_settings_tab()
        self.tab_widget.addTab(self.system_settings_tab, tr.get_settings('system_settings', 'إعدادات النظام'))

        # Import/Export settings tab
        self.import_export_tab = self.create_import_export_tab()
        self.tab_widget.addTab(self.import_export_tab, tr.get_settings('import_export', 'استيراد/تصدير'))

        # Advanced settings tab (if available)
        if ADVANCED_SETTINGS_AVAILABLE:
            self.advanced_settings_tab = SettingsManager()
            self.tab_widget.addTab(self.advanced_settings_tab, tr.get_settings('advanced_settings', 'الإعدادات المتقدمة'))

        main_layout.addWidget(self.tab_widget)
        self.setLayout(main_layout)

    def create_basic_settings_tab(self):
        """Create basic settings tab."""
        tab = QWidget()
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout()

        # Language settings
        from travelpro.core.translations import translation_manager as tr
        lang_group = QGroupBox(tr.get_settings('general_settings', 'Paramètres de langue'))
        lang_layout = QFormLayout()

        self.language_combo = QComboBox()
        # Order languages with Français first by default
        self.language_combo.addItems(["Français", "English", "العربية"])
        self.language_combo.currentTextChanged.connect(self.on_language_changed)
        lang_layout.addRow(tr.get_settings('language', 'Langue:'), self.language_combo)

        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems(["dd/MM/yyyy", "MM/dd/yyyy", "yyyy-MM-dd"])
        lang_layout.addRow(tr.get_settings('date_format', 'Format de date:'), self.date_format_combo)

        lang_group.setLayout(lang_layout)
        layout.addWidget(lang_group)

        # Currency settings
        currency_group = QGroupBox(tr.get_settings('financial_settings', 'Paramètres financiers'))
        currency_layout = QFormLayout()

        self.default_currency = QComboBox()
        self.default_currency.addItems(["EUR", "USD", "TND", "MAD", "DZD"])
        self.default_currency.currentTextChanged.connect(self.on_currency_changed)
        currency_layout.addRow(tr.get_settings('currency', 'Devise:'), self.default_currency)

        self.currency_symbol = QLineEdit("€")
        currency_layout.addRow(tr.get_settings('currency_symbol', 'Symbole de devise:'), self.currency_symbol)

        self.decimal_places = QSpinBox()
        self.decimal_places.setRange(0, 4)
        self.decimal_places.setValue(2)
        currency_layout.addRow(tr.get_settings('decimal_places', 'Décimales:'), self.decimal_places)

        currency_group.setLayout(currency_layout)
        layout.addWidget(currency_group)

        # Display settings
        display_group = QGroupBox(tr.get_settings('appearance', 'Apparence'))
        display_layout = QFormLayout()

        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["فاتح", "داكن", "تلقائي"])
        self.theme_combo.currentTextChanged.connect(self.on_theme_changed)
        display_layout.addRow(tr.get_settings('theme', 'Thème:'), self.theme_combo)

        self.font_size = QSpinBox()
        self.font_size.setRange(8, 24)
        self.font_size.setValue(12)
        self.font_size.valueChanged.connect(self.on_font_size_changed)
        display_layout.addRow(tr.get_settings('font_size', 'Taille de police:'), self.font_size)

        self.show_tooltips = QCheckBox(tr.get_settings('show_tooltips', 'Afficher les info-bulles'))
        self.show_tooltips.setChecked(True)
        display_layout.addRow(self.show_tooltips)

        # Window settings
        self.remember_window_size = QCheckBox(tr.get_settings('remember_window_size', 'Mémoriser la taille de la fenêtre'))
        display_layout.addRow(self.remember_window_size)

        self.auto_save_settings = QCheckBox(tr.get_settings('auto_save_settings', 'Enregistrement automatique des paramètres'))
        self.auto_save_settings.setChecked(True)
        display_layout.addRow(self.auto_save_settings)
        self.show_language_selector = QCheckBox(tr.get_settings('show_language_selector', "Afficher le sélecteur de langue au démarrage"))
        display_layout.addRow(self.show_language_selector)


        display_group.setLayout(display_layout)
        layout.addWidget(display_group)
        # Translator helper
        try:
            from travelpro.core.translations import translation_manager as tr
        except Exception:
            class _TR:
                def get_settings(self, k, d=None):
                    return d or k
            tr = _TR()


        # Save button
        save_btn = QPushButton(tr.get_settings('save_basic', 'Enregistrer'))
        save_btn.clicked.connect(self.save_basic_settings)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(save_btn)

        layout.addStretch()
        scroll_widget.setLayout(layout)
        scroll.setWidget(scroll_widget)
        # Translator helper for company tab
        try:
            from travelpro.core.translations import translation_manager as tr
        except Exception:
            class _TR:
                def get_settings(self, k, d=None):
                    return d or k
            tr = _TR()

        scroll.setWidgetResizable(True)

        tab_layout = QVBoxLayout()
        tab_layout.addWidget(scroll)
        tab.setLayout(tab_layout)
        return tab

    def create_company_settings_tab(self):
        """Create company settings tab."""
        tab = QWidget()
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout()

        # Company information
        try:
            from travelpro.core.translations import translation_manager as tr
        except Exception:
            class _TR:
                def get_settings(self, k, d=None):
                    return d or k
            tr = _TR()
        company_group = QGroupBox(tr.get_settings('company_info', "Informations sur l'entreprise"))
        company_layout = QFormLayout()

        self.company_name = QLineEdit()
        self.company_name.setPlaceholderText("TravelPro Agency")
        company_layout.addRow(tr.get_settings('company_name', "Nom de l'entreprise:"), self.company_name)

        self.company_address = QTextEdit()
        self.company_address.setMaximumHeight(80)
        self.company_address.setPlaceholderText("Adresse complète de l'entreprise")
        company_layout.addRow(tr.get_settings('address', 'Adresse:'), self.company_address)

        self.company_phone = QLineEdit()
        self.company_phone.setPlaceholderText("ex: +33 6 12 34 56 78")
        company_layout.addRow(tr.get_settings('phone', 'Téléphone:'), self.company_phone)

        self.company_email = QLineEdit()
        self.company_email.setPlaceholderText("<EMAIL>")
        company_layout.addRow(tr.get_settings('email', 'Email:'), self.company_email)

        self.company_website = QLineEdit()
        self.company_website.setPlaceholderText("www.travelpro.com")
        company_layout.addRow(tr.get_settings('website', 'Site web:'), self.company_website)

        self.tax_number = QLineEdit()
        self.tax_number.setPlaceholderText("Numéro d'identification fiscale")
        company_layout.addRow(tr.get_settings('tax_number', 'Numéro fiscal:'), self.tax_number)

        # License information
        self.license_number = QLineEdit()
        self.license_number.setPlaceholderText("Numéro de licence de l'agence")
        company_layout.addRow(tr.get_settings('license_number', 'Numéro de licence:'), self.license_number)

        company_group.setLayout(company_layout)
        layout.addWidget(company_group)

        # Business settings
        business_group = QGroupBox(tr.get_settings('business_settings', 'Paramètres métier'))
        business_layout = QFormLayout()

        self.default_commission = QDoubleSpinBox()
        self.default_commission.setRange(0, 100)
        self.default_commission.setSuffix(" %")
        self.default_commission.setValue(10.0)
        business_layout.addRow(tr.get_settings('default_commission', 'Commission par défaut:'), self.default_commission)

        self.tax_rate = QDoubleSpinBox()
        self.tax_rate.setRange(0, 100)
        self.tax_rate.setSuffix(" %")
        self.tax_rate.setValue(20.0)
        business_layout.addRow(tr.get_settings('default_tax_rate', 'Taux de taxe par défaut:'), self.tax_rate)

        self.invoice_prefix = QLineEdit("INV")
        business_layout.addRow(tr.get_settings('invoice_prefix', 'Préfixe de facture:'), self.invoice_prefix)

        # Working hours
        self.working_hours_start = QComboBox()
        self.working_hours_start.addItems([f"{i:02d}:00" for i in range(24)])
        self.working_hours_start.setCurrentText("08:00")
        business_layout.addRow(tr.get_settings('working_hours_start', 'Début des heures de travail:'), self.working_hours_start)

        self.working_hours_end = QComboBox()
        self.working_hours_end.addItems([f"{i:02d}:00" for i in range(24)])
        self.working_hours_end.setCurrentText("18:00")
        business_layout.addRow(tr.get_settings('working_hours_end', 'Fin des heures de travail:'), self.working_hours_end)

        # Backup settings
        self.auto_backup = QCheckBox(tr.get_settings('auto_backup', 'Sauvegarde automatique'))
        self.auto_backup.setChecked(True)
        business_layout.addRow(self.auto_backup)

        self.backup_frequency = QComboBox()
        self.backup_frequency.addItems([tr.get_settings('daily', 'Quotidien'), tr.get_settings('weekly', 'Hebdomadaire'), tr.get_settings('monthly', 'Mensuel')])
        self.backup_frequency.setCurrentIndex(0)
        business_layout.addRow(tr.get_settings('backup_frequency', 'Fréquence de sauvegarde:'), self.backup_frequency)

        business_group.setLayout(business_layout)
        layout.addWidget(business_group)

        # Save button
        save_btn = QPushButton("حفظ بيانات الشركة")
        save_btn.clicked.connect(self.save_company_settings)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(save_btn)

        layout.addStretch()
        scroll_widget.setLayout(layout)
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)

        tab_layout = QVBoxLayout()
        tab_layout.addWidget(scroll)
        tab.setLayout(tab_layout)
        return tab

    def create_system_settings_tab(self):
        """Create system settings tab."""
        tab = QWidget()
        scroll = QScrollArea()
        scroll_widget = QWidget()
        layout = QVBoxLayout()

        # Database settings
        db_group = QGroupBox("إعدادات قاعدة البيانات")
        db_layout = QFormLayout()

        self.db_path = QLineEdit("wepro.db")
        self.db_path.setReadOnly(True)
        db_layout.addRow("مسار قاعدة البيانات:", self.db_path)

        self.db_echo = QCheckBox("عرض استعلامات SQL")
        db_layout.addRow(self.db_echo)

        self.auto_vacuum = QCheckBox("تنظيف تلقائي لقاعدة البيانات")
        self.auto_vacuum.setChecked(True)
        db_layout.addRow(self.auto_vacuum)

        self.connection_timeout = QSpinBox()
        self.connection_timeout.setRange(5, 120)
        self.connection_timeout.setValue(20)
        self.connection_timeout.setSuffix(" ثانية")
        db_layout.addRow("مهلة الاتصال:", self.connection_timeout)

        db_group.setLayout(db_layout)
        layout.addWidget(db_group)

        # Security settings
        security_group = QGroupBox("إعدادات الأمان")
        security_layout = QFormLayout()

        self.session_timeout = QSpinBox()
        self.session_timeout.setRange(5, 480)
        self.session_timeout.setValue(60)
        self.session_timeout.setSuffix(" دقيقة")
        security_layout.addRow("انتهاء الجلسة:", self.session_timeout)

        self.password_min_length = QSpinBox()
        self.password_min_length.setRange(4, 20)
        self.password_min_length.setValue(6)
        security_layout.addRow("الحد الأدنى لطول كلمة المرور:", self.password_min_length)

        self.password_complexity = QCheckBox("كلمات مرور معقدة")
        security_layout.addRow(self.password_complexity)

        self.login_attempts = QSpinBox()
        self.login_attempts.setRange(1, 10)
        self.login_attempts.setValue(3)
        security_layout.addRow("محاولات الدخول القصوى:", self.login_attempts)

        self.auto_logout = QCheckBox("تسجيل خروج تلقائي عند عدم النشاط")
        self.auto_logout.setChecked(True)
        security_layout.addRow(self.auto_logout)

        security_group.setLayout(security_layout)
        layout.addWidget(security_group)

        # Performance settings
        perf_group = QGroupBox("إعدادات الأداء")
        perf_layout = QFormLayout()

        self.cache_size = QSpinBox()
        self.cache_size.setRange(10, 1000)
        self.cache_size.setValue(100)
        self.cache_size.setSuffix(" MB")
        perf_layout.addRow("حجم التخزين المؤقت:", self.cache_size)

        self.max_records = QSpinBox()
        self.max_records.setRange(100, 10000)
        self.max_records.setValue(1000)
        perf_layout.addRow("الحد الأقصى للسجلات:", self.max_records)

        self.enable_logging = QCheckBox("تفعيل نظام السجلات")
        self.enable_logging.setChecked(True)
        perf_layout.addRow(self.enable_logging)

        self.log_level = QComboBox()
        self.log_level.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.log_level.setCurrentText("INFO")
        perf_layout.addRow("مستوى السجلات:", self.log_level)

        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)

        # UI settings
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_layout = QFormLayout()

        self.window_width = QSpinBox()
        self.window_width.setRange(800, 2000)
        self.window_width.setValue(1200)
        ui_layout.addRow("عرض النافذة:", self.window_width)

        self.window_height = QSpinBox()
        self.window_height.setRange(600, 1500)
        self.window_height.setValue(750)
        ui_layout.addRow("ارتفاع النافذة:", self.window_height)

        self.sidebar_width = QSpinBox()
        self.sidebar_width.setRange(150, 300)
        self.sidebar_width.setValue(200)
        ui_layout.addRow("عرض الشريط الجانبي:", self.sidebar_width)

        ui_group.setLayout(ui_layout)
        layout.addWidget(ui_group)

        # Save button
        save_btn = QPushButton("حفظ إعدادات النظام")
        save_btn.clicked.connect(self.save_system_settings)
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(save_btn)

        layout.addStretch()
        scroll_widget.setLayout(layout)
        scroll.setWidget(scroll_widget)
        scroll.setWidgetResizable(True)

        tab_layout = QVBoxLayout()
        tab_layout.addWidget(scroll)
        tab.setLayout(tab_layout)
        return tab

    def create_import_export_tab(self):
        """Create import/export settings tab."""
        tab = QWidget()
        layout = QVBoxLayout()

        # Title
        title_label = QLabel("إدارة الإعدادات")
        title_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        """)
        layout.addWidget(title_label)

        # Export settings group
        export_group = QGroupBox("تصدير الإعدادات")
        export_layout = QVBoxLayout()

        export_desc = QLabel("احفظ نسخة من إعداداتك الحالية في ملف خارجي")
        export_desc.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        export_layout.addWidget(export_desc)

        export_btn = QPushButton("تصدير الإعدادات")
        export_btn.clicked.connect(self.export_settings)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        export_layout.addWidget(export_btn)

        export_group.setLayout(export_layout)
        layout.addWidget(export_group)

        # Import settings group
        import_group = QGroupBox("استيراد الإعدادات")
        import_layout = QVBoxLayout()

        import_desc = QLabel("استورد إعدادات من ملف محفوظ مسبقاً")
        import_desc.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        import_layout.addWidget(import_desc)

        import_btn = QPushButton("استيراد الإعدادات")
        import_btn.clicked.connect(self.import_settings)
        import_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        import_layout.addWidget(import_btn)

        import_group.setLayout(import_layout)
        layout.addWidget(import_group)

        # Reset settings group
        reset_group = QGroupBox("إعادة تعيين الإعدادات")
        reset_layout = QVBoxLayout()

        reset_desc = QLabel("أعد جميع الإعدادات إلى القيم الافتراضية")
        reset_desc.setStyleSheet("color: #e74c3c; margin-bottom: 10px;")
        reset_layout.addWidget(reset_desc)

        reset_btn = QPushButton("إعادة تعيين الإعدادات")
        reset_btn.clicked.connect(self.reset_settings)
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        reset_layout.addWidget(reset_btn)

        reset_group.setLayout(reset_layout)
        layout.addWidget(reset_group)

        # Validation group
        validation_group = QGroupBox("التحقق من الإعدادات")
        validation_layout = QVBoxLayout()

        validation_desc = QLabel("تحقق من صحة الإعدادات الحالية")
        validation_desc.setStyleSheet("color: #7f8c8d; margin-bottom: 10px;")
        validation_layout.addWidget(validation_desc)

        validate_btn = QPushButton("التحقق من الإعدادات")
        validate_btn.clicked.connect(self.validate_settings)
        validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        validation_layout.addWidget(validate_btn)

        validation_group.setLayout(validation_layout)
        layout.addWidget(validation_group)

        # Current config info
        info_group = QGroupBox("معلومات الإعدادات الحالية")
        info_layout = QVBoxLayout()

        self.config_info_label = QLabel("جاري تحميل معلومات الإعدادات...")
        self.config_info_label.setStyleSheet("""
            QLabel {
                border: 1px solid #bdc3c7;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                font-family: monospace;
            }
        """)
        info_layout.addWidget(self.config_info_label)

        refresh_info_btn = QPushButton("تحديث المعلومات")
        refresh_info_btn.clicked.connect(self.update_config_info)
        refresh_info_btn.setStyleSheet("""
            QPushButton {
                background-color: #34495e;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2c3e50;
            }
        """)
        info_layout.addWidget(refresh_info_btn)

        info_group.setLayout(info_layout)
        layout.addWidget(info_group)

        layout.addStretch()
        tab.setLayout(layout)

        # Update config info initially
        self.update_config_info()

        return tab

    def load_settings(self):
        """Load settings from config."""
        if not self.config:
            return

        try:
            # Load basic settings
            if hasattr(self, 'language_combo'):
                language_map = {"العربية": "ar", "Français": "fr", "English": "en"}
                current_lang = self.config.get('language', 'fr')
                for display_name, code in language_map.items():
                    if code == current_lang:
                        self.language_combo.setCurrentText(display_name)
                        break

                self.date_format_combo.setCurrentText(self.config.get('ui.date_format', 'dd/MM/yyyy'))
                self.default_currency.setCurrentText(self.config.get('business.currency', 'EUR'))

                # Update currency symbol based on currency
                self.update_currency_symbol()

                self.decimal_places.setValue(self.config.get('ui.decimal_places', 2))

                theme_map = {"فاتح": "light", "داكن": "dark", "تلقائي": "auto"}
                current_theme = self.config.get('theme', 'light')
                for display_name, code in theme_map.items():
                    if code == current_theme:
                        self.theme_combo.setCurrentText(display_name)
                        break

                self.font_size.setValue(self.config.get('ui.font_size', 12))
                self.show_language_selector.setChecked(self.config.get('ui.show_language_selector', False))

                self.show_tooltips.setChecked(self.config.get('ui.show_tooltips', True))
                self.remember_window_size.setChecked(self.config.get('ui.remember_window_size', True))
                self.auto_save_settings.setChecked(self.config.get('ui.auto_save_settings', True))

            # Load company settings
            if hasattr(self, 'company_name'):
                self.company_name.setText(self.config.get('business.company_name', ''))
                self.company_address.setPlainText(self.config.get('business.company_address', ''))
                self.company_phone.setText(self.config.get('business.company_phone', ''))
                self.company_email.setText(self.config.get('business.company_email', ''))
                self.company_website.setText(self.config.get('business.company_website', ''))
                self.tax_number.setText(self.config.get('business.tax_number', ''))
                self.license_number.setText(self.config.get('business.license_number', ''))

                self.default_commission.setValue(self.config.get('business.default_commission', 10.0))
                self.tax_rate.setValue(self.config.get('business.tax_rate', 0.20) * 100)
                self.invoice_prefix.setText(self.config.get('business.invoice_prefix', 'INV'))

                self.working_hours_start.setCurrentText(self.config.get('business.working_hours_start', '08:00'))
                self.working_hours_end.setCurrentText(self.config.get('business.working_hours_end', '18:00'))

                self.auto_backup.setChecked(self.config.get('backup.enabled', True))
                self.backup_frequency.setCurrentText(self.config.get('backup.frequency', 'يومياً'))

            # Load system settings
            if hasattr(self, 'session_timeout'):
                self.db_path.setText(self.config.get_database_url().replace('sqlite:///./', ''))
                self.db_echo.setChecked(self.config.get('database.echo', False))
                self.auto_vacuum.setChecked(self.config.get('database.auto_vacuum', True))
                self.connection_timeout.setValue(self.config.get('database.connection_timeout', 20))

                self.session_timeout.setValue(self.config.get('security.session_timeout', 3600) // 60)
                self.password_min_length.setValue(self.config.get('security.password_min_length', 6))
                self.password_complexity.setChecked(self.config.get('security.password_complexity', False))
                self.login_attempts.setValue(self.config.get('security.max_login_attempts', 3))
                self.auto_logout.setChecked(self.config.get('security.auto_logout', True))

                self.cache_size.setValue(self.config.get('performance.cache_size', 100))
                self.max_records.setValue(self.config.get('performance.max_records', 1000))
                self.enable_logging.setChecked(self.config.get('logging.enabled', True))
                self.log_level.setCurrentText(self.config.get('logging.level', 'INFO'))

                self.window_width.setValue(self.config.get('ui.window_width', 1200))
                self.window_height.setValue(self.config.get('ui.window_height', 750))
                self.sidebar_width.setValue(self.config.get('ui.sidebar_width', 200))

        except Exception as e:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite") + f"\n{str(e)}"
            except Exception:
                _title = 'Erreur'
                _text = f"Une erreur s'est produite\n{str(e)}"
            QMessageBox.warning(self, _title, _text)

    def save_basic_settings(self):
        """Save basic settings."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        try:
            # Map display names to codes
            language_map = {"العربية": "ar", "Français": "fr", "English": "en"}
            theme_map = {"فاتح": "light", "داكن": "dark", "تلقائي": "auto"}

            self.config.set('language', language_map.get(self.language_combo.currentText(), 'fr'))
            self.config.set('ui.date_format', self.date_format_combo.currentText())
            self.config.set('business.currency', self.default_currency.currentText())
            self.config.set('ui.decimal_places', self.decimal_places.value())
            self.config.set('theme', theme_map.get(self.theme_combo.currentText(), 'light'))
            self.config.set('ui.font_size', self.font_size.value())
            self.config.set('ui.show_tooltips', self.show_tooltips.isChecked())
            self.config.set('ui.remember_window_size', self.remember_window_size.isChecked())
            self.config.set('ui.auto_save_settings', self.auto_save_settings.isChecked())
            self.config.set('ui.show_language_selector', self.show_language_selector.isChecked())

            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('operation_successful', 'Succès')
                _text = tr.get_system('data_saved', 'Données enregistrées avec succès')
            except Exception:
                _title = 'Succès'
                _text = 'Données enregistrées avec succès'
            QMessageBox.information(self, _title, _text)

        except Exception as e:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite") + f"\n{str(e)}"
            except Exception:
                _title = 'Erreur'
                _text = f"Une erreur s'est produite\n{str(e)}"
            QMessageBox.critical(self, _title, _text)

    def save_company_settings(self):
        """Save company settings."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        try:
            self.config.set('business.company_name', self.company_name.text())
            self.config.set('business.company_address', self.company_address.toPlainText())
            self.config.set('business.company_phone', self.company_phone.text())
            self.config.set('business.company_email', self.company_email.text())
            self.config.set('business.company_website', self.company_website.text())
            self.config.set('business.tax_number', self.tax_number.text())
            self.config.set('business.license_number', self.license_number.text())

            self.config.set('business.default_commission', self.default_commission.value())
            self.config.set('business.tax_rate', self.tax_rate.value() / 100)
            self.config.set('business.invoice_prefix', self.invoice_prefix.text())

            self.config.set('business.working_hours_start', self.working_hours_start.currentText())
            self.config.set('business.working_hours_end', self.working_hours_end.currentText())

            self.config.set('backup.enabled', self.auto_backup.isChecked())
            self.config.set('backup.frequency', self.backup_frequency.currentText())

            QMessageBox.information(self, "نجح", "تم حفظ بيانات الشركة بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ بيانات الشركة:\n{str(e)}")

    def save_system_settings(self):
        """Save system settings."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        try:
            self.config.set('database.echo', self.db_echo.isChecked())
            self.config.set('database.auto_vacuum', self.auto_vacuum.isChecked())
            self.config.set('database.connection_timeout', self.connection_timeout.value())

            self.config.set('security.session_timeout', self.session_timeout.value() * 60)
            self.config.set('security.password_min_length', self.password_min_length.value())
            self.config.set('security.password_complexity', self.password_complexity.isChecked())
            self.config.set('security.max_login_attempts', self.login_attempts.value())
            self.config.set('security.auto_logout', self.auto_logout.isChecked())

            self.config.set('performance.cache_size', self.cache_size.value())
            self.config.set('performance.max_records', self.max_records.value())
            self.config.set('logging.enabled', self.enable_logging.isChecked())
            self.config.set('logging.level', self.log_level.currentText())

            self.config.set('ui.window_width', self.window_width.value())
            self.config.set('ui.window_height', self.window_height.value())
            self.config.set('ui.sidebar_width', self.sidebar_width.value())

            QMessageBox.information(self, "نجح", "تم حفظ إعدادات النظام بنجاح!")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ إعدادات النظام:\n{str(e)}")

    # Event handlers for real-time updates
    def on_language_changed(self, language):
        """Handle language change."""
        if self.auto_save_settings.isChecked():
            self.save_basic_settings()

    def on_currency_changed(self, currency):
        """Handle currency change."""
        self.update_currency_symbol()
        if self.auto_save_settings.isChecked():
            self.save_basic_settings()

    def on_theme_changed(self, theme):
        """Handle theme change."""
        if self.auto_save_settings.isChecked():
            self.save_basic_settings()

    def on_font_size_changed(self, size):
        """Handle font size change."""
        if self.auto_save_settings.isChecked():
            self.save_basic_settings()

    def update_currency_symbol(self):
        """Update currency symbol based on selected currency."""
        currency_symbols = {
            "EUR": "€",
            "USD": "$",
            "TND": "د.ت",
            "MAD": "د.م",
            "DZD": "د.ج"
        }

        current_currency = self.default_currency.currentText()
        symbol = currency_symbols.get(current_currency, "€")
        self.currency_symbol.setText(symbol)

    def export_settings(self):
        """Export settings to file."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "تصدير الإعدادات",
            "travelpro_settings.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            if self.config.export_settings(file_path):
                QMessageBox.information(self, "نجح", f"تم تصدير الإعدادات بنجاح إلى:\n{file_path}")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في تصدير الإعدادات")

    def import_settings(self):
        """Import settings from file."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        from PyQt5.QtWidgets import QFileDialog
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "استيراد الإعدادات",
            "",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد",
                "هل أنت متأكد من استيراد الإعدادات؟\nسيتم استبدال الإعدادات الحالية.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if self.config.import_settings(file_path):
                    QMessageBox.information(self, "نجح", "تم استيراد الإعدادات بنجاح!\nسيتم إعادة تحميل الإعدادات.")
                    self.load_settings()
                    self.update_config_info()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في استيراد الإعدادات")

    def reset_settings(self):
        """Reset all settings to defaults."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        reply = QMessageBox.question(
            self,
            "تأكيد إعادة التعيين",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟\nسيتم فقدان جميع الإعدادات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.config.reset()
            QMessageBox.information(self, "نجح", "تم إعادة تعيين الإعدادات بنجاح!\nسيتم إعادة تحميل الإعدادات.")
            self.load_settings()
            self.update_config_info()

    def validate_settings(self):
        """Validate current settings."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            QMessageBox.warning(self, _title, _text)
            return

        errors = self.config.validate_settings()

        if not errors:
            QMessageBox.information(self, "التحقق من الإعدادات", "✅ جميع الإعدادات صحيحة!")
        else:
            error_text = "تم العثور على الأخطاء التالية:\n\n"
            for field, error in errors.items():
                error_text += f"• {error}\n"

            QMessageBox.warning(self, "أخطاء في الإعدادات", error_text)

    def update_config_info(self):
        """Update configuration information display."""
        if not self.config:
            try:
                from travelpro.core.translations import translation_manager as tr
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _text = "Une erreur s'est produite"
            self.config_info_label.setText(_text)
            return

        try:
            import os
            config_file = "config.json"

            info_text = "معلومات ملف الإعدادات:\n"
            info_text += "=" * 30 + "\n"

            if os.path.exists(config_file):
                stat = os.stat(config_file)
                import time
                modified_time = time.ctime(stat.st_mtime)
                size = stat.st_size

                info_text += f"📁 المسار: {os.path.abspath(config_file)}\n"
                info_text += f"📅 آخر تعديل: {modified_time}\n"
                info_text += f"📊 الحجم: {size} بايت\n"
            else:
                info_text += "❌ ملف الإعدادات غير موجود\n"

            info_text += "\nالإعدادات الحالية:\n"
            info_text += "=" * 20 + "\n"

            # Basic info
            info_text += f"🌐 اللغة: {self.config.get_language()}\n"
            info_text += f"🎨 المظهر: {self.config.get_theme()}\n"
            info_text += f"💰 العملة: {self.config.get('business.currency')}\n"

            # Company info
            company_name = self.config.get('business.company_name')
            if company_name:
                info_text += f"🏢 الشركة: {company_name}\n"

            # Window size
            width, height = self.config.get_window_size()
            info_text += f"🖥️ حجم النافذة: {width}x{height}\n"

            # Security
            session_timeout = self.config.get('security.session_timeout', 3600) // 60
            info_text += f"🔒 انتهاء الجلسة: {session_timeout} دقيقة\n"

            self.config_info_label.setText(info_text)

        except Exception as e:
            try:
                from travelpro.core.translations import translation_manager as tr
                _text = tr.get_system('generic_error', "Une erreur s'est produite") + f"\n{str(e)}"
            except Exception:
                _text = f"Une erreur s'est produite\n{str(e)}"
            self.config_info_label.setText(_text)
