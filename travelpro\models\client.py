"""
TravelPro - Modèle Client
-------------------------
Ce module définit le modèle de données pour les clients.
"""

import logging
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from travelpro.core.database import Base

logger = logging.getLogger(__name__)

class Client(Base):
    """Modèle de données pour les clients de l'agence."""
    
    __tablename__ = "clients"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(10), unique=True, index=True)
    civility = Column(String(10))
    first_name = Column(String(50), nullable=False)
    last_name = Column(String(50), nullable=False)
    email = Column(String(100), unique=True, index=True)
    phone = Column(String(20))
    mobile = Column(String(20))
    address = Column(Text)
    postal_code = Column(String(10))
    city = Column(String(50))
    country = Column(String(50))
    birth_date = Column(DateTime)
    notes = Column(Text)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relations
    reservations = relationship("Reservation", back_populates="client")
    loyalty_card = relationship("LoyaltyCard", back_populates="client", uselist=False)
    
    def __repr__(self):
        """Représentation textuelle du client."""
        return f"<Client {self.code}: {self.first_name} {self.last_name}>"
    
    @property
    def full_name(self):
        """Retourne le nom complet du client."""
        return f"{self.first_name} {self.last_name}"


class LoyaltyCard(Base):
    """Modèle de données pour les cartes de fidélité."""
    
    __tablename__ = "loyalty_cards"
    
    id = Column(Integer, primary_key=True, index=True)
    card_number = Column(String(20), unique=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), unique=True)
    points = Column(Integer, default=0)
    status = Column(String(20), default="Standard")  # Standard, Silver, Gold, Platinum
    issue_date = Column(DateTime(timezone=True), server_default=func.now())
    expiry_date = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    
    # Relations
    client = relationship("Client", back_populates="loyalty_card")
    transactions = relationship("LoyaltyTransaction", back_populates="loyalty_card")
    
    def __repr__(self):
        """Représentation textuelle de la carte de fidélité."""
        return f"<LoyaltyCard {self.card_number}: {self.status} ({self.points} points)>"


class LoyaltyTransaction(Base):
    """Modèle de données pour les transactions de points de fidélité."""
    
    __tablename__ = "loyalty_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    loyalty_card_id = Column(Integer, ForeignKey("loyalty_cards.id"))
    reservation_id = Column(Integer, ForeignKey("reservations.id"), nullable=True)
    points = Column(Integer, nullable=False)  # Positif pour ajout, négatif pour utilisation
    description = Column(String(200))
    transaction_date = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relations
    loyalty_card = relationship("LoyaltyCard", back_populates="transactions")
    reservation = relationship("Reservation", back_populates="loyalty_transactions")
    
    def __repr__(self):
        """Représentation textuelle de la transaction de points."""
        action = "Ajout" if self.points > 0 else "Utilisation"
        return f"<LoyaltyTransaction {action} de {abs(self.points)} points>"
