"""
TravelPro - Écran de connexion
------------------------------
Ce module contient l'écran de connexion de l'application.
"""

import logging
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QFrame, QMessageBox, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap

from travelpro.core.auth import AuthManager

logger = logging.getLogger(__name__)

class LoginScreen(QWidget):
    """Écran de connexion de l'application TravelPro."""
    
    # Signal émis lorsque la connexion est réussie
    login_successful = pyqtSignal(str, str)  # username, token
    
    def __init__(self):
        """Initialisation de l'écran de connexion."""
        super().__init__()
        
        self.auth_manager = AuthManager()
        
        # Configuration de la fenêtre
        self.setWindowTitle("TravelPro - Connexion")
        self.setMinimumSize(400, 500)
        
        # Création des widgets et du layout
        self.setup_ui()
        
        # Connexion des signaux
        self.connect_signals()
        
        logger.info("Écran de connexion initialisé")
    
    def setup_ui(self):
        """Configure l'interface utilisateur."""
        # Layout principal
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(40, 40, 40, 40)
        self.main_layout.setSpacing(20)
        
        # Logo et titre
        self.title_layout = QVBoxLayout()
        self.title_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.logo_label = QLabel()
        # Ici, on pourrait charger un logo réel
        # self.logo_label.setPixmap(QPixmap("path/to/logo.png"))
        self.logo_label.setText("[LOGO]")
        self.logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.title_label = QLabel("TravelPro")
        self.title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
        self.title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.subtitle_label = QLabel("Logiciel de Gestion d'Agence de Voyages")
        self.subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.title_layout.addWidget(self.logo_label)
        self.title_layout.addWidget(self.title_label)
        self.title_layout.addWidget(self.subtitle_label)
        
        self.main_layout.addLayout(self.title_layout)
        
        # Séparateur
        self.separator = QFrame()
        self.separator.setFrameShape(QFrame.Shape.HLine)
        self.separator.setFrameShadow(QFrame.Shadow.Sunken)
        self.main_layout.addWidget(self.separator)
        
        # Formulaire de connexion
        self.form_layout = QVBoxLayout()
        
        # Nom d'utilisateur
        self.username_label = QLabel("Nom d'utilisateur:")
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Entrez votre nom d'utilisateur")
        
        # Mot de passe
        self.password_label = QLabel("Mot de passe:")
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("Entrez votre mot de passe")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        
        # Se souvenir de moi
        self.remember_checkbox = QCheckBox("Se souvenir de moi")
        
        # Bouton de connexion
        self.login_button = QPushButton("Connexion")
        self.login_button.setMinimumHeight(40)
        
        # Lien d'inscription
        self.register_layout = QHBoxLayout()
        self.register_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        self.register_label = QLabel("Pas encore de compte?")
        self.register_button = QPushButton("S'inscrire")
        self.register_button.setFlat(True)
        
        self.register_layout.addWidget(self.register_label)
        self.register_layout.addWidget(self.register_button)
        
        # Ajout des widgets au layout du formulaire
        self.form_layout.addWidget(self.username_label)
        self.form_layout.addWidget(self.username_input)
        self.form_layout.addSpacing(10)
        self.form_layout.addWidget(self.password_label)
        self.form_layout.addWidget(self.password_input)
        self.form_layout.addSpacing(10)
        self.form_layout.addWidget(self.remember_checkbox)
        self.form_layout.addSpacing(20)
        self.form_layout.addWidget(self.login_button)
        self.form_layout.addSpacing(10)
        self.form_layout.addLayout(self.register_layout)
        
        self.main_layout.addLayout(self.form_layout)
        
        # Espace flexible
        self.main_layout.addStretch()
        
        # Pied de page
        self.footer_label = QLabel("© 2025 Manus AI - TravelPro v0.1.0")
        self.footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.main_layout.addWidget(self.footer_label)
    
    def connect_signals(self):
        """Connecte les signaux aux slots."""
        self.login_button.clicked.connect(self.on_login_clicked)
        self.register_button.clicked.connect(self.on_register_clicked)
        self.password_input.returnPressed.connect(self.on_login_clicked)
    
    def on_login_clicked(self):
        """Gère le clic sur le bouton de connexion."""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(
                self,
                "Champs requis",
                "Veuillez remplir tous les champs."
            )
            return
        
        # Tentative d'authentification
        token = self.auth_manager.authenticate(username, password)
        
        if token:
            logger.info(f"Connexion réussie pour l'utilisateur: {username}")
            self.login_successful.emit(username, token)
        else:
            logger.warning(f"Échec de connexion pour l'utilisateur: {username}")
            QMessageBox.critical(
                self,
                "Échec de connexion",
                "Nom d'utilisateur ou mot de passe incorrect."
            )
    
    def on_register_clicked(self):
        """Gère le clic sur le bouton d'inscription."""
        # Ici, on pourrait ouvrir un écran d'inscription
        QMessageBox.information(
            self,
            "Inscription",
            "La fonctionnalité d'inscription sera disponible prochainement."
        )
