from PyQt5.QtWidgets import QFrame, QHBoxLayout, QLineEdit, QPushButton, QComboBox
from PyQt5.QtGui import QFont
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
import qtawesome as qta

# Import du système de traduction
try:
    from travelpro.core.translations import translation_manager as tr
except ImportError:
    # Fallback si le système de traduction n'est pas disponible
    class FallbackTranslation:
        def get_main(self, key, default=None):
            return default or key
    tr = FallbackTranslation()

class Header(QFrame):
    # Signal to communicate with main window
    settings_clicked = pyqtSignal()
    language_changed = pyqtSignal(str)  # 'fr' | 'en' | 'ar'

    def __init__(self):
        super().__init__()
        self.setStyleSheet("""
            QFrame {
                background: #fff;
                border-bottom: 1px solid #e0e0e0;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 15px;
                padding: 5px 15px;
                font-size: 14px;
            }
        """)
        layout = QHBoxLayout()

        # Barre de recherche
        self.search_bar = QLineEdit()
        self.search_bar.setPlaceholderText(tr.get_main("search", "Rechercher..."))
        self.search_bar.setFixedWidth(250)

        # Icônes
        search_icon = qta.icon("mdi.magnify", color="#777")
        self.search_bar.addAction(search_icon, QLineEdit.LeadingPosition)

        # Bouton nouveau
        self.btn_new = QPushButton(f"+ {tr.get_main('add', 'Nouveau')}")
        self.btn_new.setIcon(qta.icon("mdi.plus", color="#fff"))
        self.btn_new.setStyleSheet("""
            QPushButton {
                background: #00ADB5;
                color: #fff;
                border-radius: 8px;
                padding: 8px 18px;
                font-size: 15px;
            }
            QPushButton:hover {
                background: #00969e;
            }
        """)

        # إشعارات وإعدادات - نظام الإشعارات الموحد
        try:
            from .unified_notifications import UnifiedNotificationSystem
            from .notifications_system import NotificationManager

            # Initialize notification manager safely
            self.notification_manager = NotificationManager()

            # Create unified notification system safely
            self.unified_notifications = UnifiedNotificationSystem(self.notification_manager)
            btn_notif = self.unified_notifications.get_widget()

            # Create demo notifications for testing (delayed)
            QTimer.singleShot(1000, self.create_demo_notifications_delayed)

        except Exception as e:
            print(f"Error initializing notifications: {e}")
            # Fallback if unified notifications not available
            btn_notif = QPushButton()
            btn_notif.setIcon(qta.icon("mdi.bell", color="#555"))
            btn_notif.setStyleSheet("QPushButton { border: none; }")
            self.unified_notifications = None

        self.btn_settings = QPushButton()
        self.btn_settings.setIcon(qta.icon("mdi.cog", color="#555"))
        self.btn_settings.setStyleSheet("""
            QPushButton {
                border: none;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
        """)
        # Language selector
        self.lang_selector = QComboBox()
        self.lang_selector.addItems(["Français", "English", "العربية"])
        # Initialize selection based on current language without triggering a change
        try:
            # use already-imported global tr
            lang = getattr(tr, 'current_language', 'fr')
        except Exception:
            try:
                from travelpro.core.config import Config
                lang = Config().get('language', 'fr')
            except Exception:
                lang = 'fr'
        index_map = {'fr': 0, 'en': 1, 'ar': 2}
        self.lang_selector.blockSignals(True)
        self.lang_selector.setCurrentIndex(index_map.get(lang, 0))
        self.lang_selector.blockSignals(False)
        self.lang_selector.currentIndexChanged.connect(self.on_language_changed)

        # Theme selector (simple)
        self.theme_selector = QComboBox()
        # أسماء الثيمات معروضة حسب اللغة الحالية
        try:
            names = {
                'modern_blue': tr.get_settings('blue_theme', 'Bleu'),
                'dark_professional': tr.get_settings('dark_theme', 'Sombre'),
                'green_nature': tr.get_settings('green_theme', 'Vert')
            }
            self.theme_key_order = ['modern_blue', 'dark_professional', 'green_nature']
            self.theme_selector.addItems([names.get(k, k) for k in self.theme_key_order])
        except Exception:
            self.theme_key_order = ['modern_blue', 'dark_professional', 'green_nature']
            self.theme_selector.addItems(["Modern Blue", "Dark Professional", "Green Nature"])
        self.theme_selector.setCurrentIndex(0)
        self.theme_selector.currentIndexChanged.connect(self.on_theme_changed)

        self.btn_settings.setToolTip(tr.get_main("settings", "Paramètres"))
        self.btn_settings.clicked.connect(self.on_settings_clicked)

        layout.addWidget(self.search_bar)
        layout.addStretch()
        layout.addWidget(self.btn_new)
        layout.addStretch()
        layout.addWidget(self.lang_selector)
        layout.addWidget(self.theme_selector)
        layout.addWidget(btn_notif)
        layout.addWidget(self.btn_settings)
        self.setLayout(layout)

    def on_settings_clicked(self):
        """Handle settings button click."""
        self.settings_clicked.emit()

    def add_notification(self, title, message, notification_type="reminder", priority="medium"):
        """Add new notification to the unified system."""
        if self.unified_notifications:
            self.unified_notifications.add_notification(title, message, notification_type, priority)

    def on_language_changed(self, idx):
        try:
            from travelpro.core.translations import translation_manager as tr
            text = self.lang_selector.currentText().lower()
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            code = "fr"
            if "arab" in text or "الع" in text:
                code = "ar"; tr.set_language("ar")
                if app: app.setLayoutDirection(Qt.RightToLeft)
            elif text.startswith("english"):
                code = "en"; tr.set_language("en")
                if app: app.setLayoutDirection(Qt.LeftToRight)
            else:
                code = "fr"; tr.set_language("fr")
                if app: app.setLayoutDirection(Qt.LeftToRight)
            # Update placeholders/titles that use tr
            self.search_bar.setPlaceholderText(tr.get_main("search", "Rechercher..."))
            self.btn_settings.setToolTip(tr.get_main("settings", "Paramètres"))
            # Update "New" button text
            try:
                self.btn_new.setText(f"+ {tr.get_main('add', 'Nouveau')}")
            except Exception:
                pass
            # Persist language selection to config
            try:
                from travelpro.core.config import Config
                cfg = Config()
                cfg.set('language', code)
            except Exception:
                pass
            # Rebuild theme names in current language (preserve selection by index)
            try:
                idx_before = self.theme_selector.currentIndex()
                self.theme_selector.blockSignals(True)
                self.theme_selector.clear()
                names = {
                    'modern_blue': tr.get_settings('blue_theme', 'Bleu'),
                    'dark_professional': tr.get_settings('dark_theme', 'Sombre'),
                    'green_nature': tr.get_settings('green_theme', 'Vert')
                }
                for key in self.theme_key_order:
                    self.theme_selector.addItem(names.get(key, key))
                self.theme_selector.setCurrentIndex(idx_before if 0 <= idx_before < self.theme_selector.count() else 0)
                self.theme_selector.blockSignals(False)
            except Exception:
                pass
            # Notify main window to refresh texts
            try:
                self.language_changed.emit(code)
            except Exception:
                pass
        except Exception as e:
            print(f"Error changing language: {e}")

    def on_theme_changed(self, idx):
        try:
            from travelpro.ui.theme_manager import ThemeManager
            from travelpro.core.config import Config
            tm = ThemeManager()
            # استخرج المفتاح بناء على الترتيب (لأن الاسم مترجم)
            idx = self.theme_selector.currentIndex()
            key = self.theme_key_order[idx] if 0 <= idx < len(self.theme_key_order) else "modern_blue"
            if tm.set_theme(key):
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    tm.apply_theme(app)
                # حفظ الاختيار في إعدادات التطبيق إلى مفتاح منفصل
                try:
                    cfg = Config()
                    cfg.set('ui.theme_key', key)
                except Exception:
                    pass
        except Exception as e:
            print(f"Error changing theme: {e}")

    def get_notification_system(self):
        """Get the unified notification system."""
        return self.unified_notifications

    def create_demo_notifications_delayed(self):
        """Create demo notifications with delay to avoid startup errors."""
        try:
            if self.unified_notifications:
                self.unified_notifications.create_demo_notifications()
        except Exception as e:
            print(f"Error creating demo notifications: {e}")