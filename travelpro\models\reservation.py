"""
TravelPro - Mo<PERSON><PERSON><PERSON>éser<PERSON>
------------------------------
Ce module définit le modèle de données pour les réservations.
"""

import logging
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Float, ForeignKey, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

from travelpro.core.database import Base

logger = logging.getLogger(__name__)

class ReservationStatus(enum.Enum):
    """Statuts possibles pour une réservation."""
    PENDING = "En attente"
    CONFIRMED = "Confirmée"
    PAID = "Payée"
    CANCELLED = "Annulée"
    COMPLETED = "Terminée"


class Reservation(Base):
    """Modèle de données pour les réservations."""
    
    __tablename__ = "reservations"
    
    id = Column(Integer, primary_key=True, index=True)
    reference = Column(String(20), unique=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"))
    status = Column(Enum(ReservationStatus), default=ReservationStatus.PENDING)
    total_amount = Column(Float, default=0.0)
    paid_amount = Column(Float, default=0.0)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relations
    client = relationship("Client", back_populates="reservations")
    items = relationship("ReservationItem", back_populates="reservation")
    payments = relationship("Payment", back_populates="reservation")
    loyalty_transactions = relationship("LoyaltyTransaction", back_populates="reservation")
    
    def __repr__(self):
        """Représentation textuelle de la réservation."""
        return f"<Reservation {self.reference}: {self.status.value}>"
    
    @property
    def balance(self):
        """Calcule le solde restant à payer."""
        return self.total_amount - self.paid_amount
    
    @property
    def is_fully_paid(self):
        """Vérifie si la réservation est entièrement payée."""
        return self.paid_amount >= self.total_amount


class ReservationItem(Base):
    """Modèle de données pour les éléments d'une réservation."""
    
    __tablename__ = "reservation_items"
    
    id = Column(Integer, primary_key=True, index=True)
    reservation_id = Column(Integer, ForeignKey("reservations.id"))
    product_id = Column(Integer, ForeignKey("products.id"))
    quantity = Column(Integer, default=1)
    unit_price = Column(Float, nullable=False)
    discount = Column(Float, default=0.0)
    description = Column(String(200))
    
    # Relations
    reservation = relationship("Reservation", back_populates="items")
    product = relationship("Product", back_populates="reservation_items")
    
    def __repr__(self):
        """Représentation textuelle de l'élément de réservation."""
        return f"<ReservationItem: {self.quantity} x {self.description}>"
    
    @property
    def total_price(self):
        """Calcule le prix total de l'élément."""
        return (self.unit_price * self.quantity) - self.discount


class Product(Base):
    """Modèle de données pour les produits touristiques."""
    
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    code = Column(String(20), unique=True, index=True)
    name = Column(String(100), nullable=False)
    category = Column(String(50))
    description = Column(Text)
    price = Column(Float, default=0.0)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relations
    reservation_items = relationship("ReservationItem", back_populates="product")
    
    def __repr__(self):
        """Représentation textuelle du produit."""
        return f"<Product {self.code}: {self.name}>"


class Payment(Base):
    """Modèle de données pour les paiements."""
    
    __tablename__ = "payments"
    
    id = Column(Integer, primary_key=True, index=True)
    reservation_id = Column(Integer, ForeignKey("reservations.id"))
    amount = Column(Float, nullable=False)
    payment_date = Column(DateTime(timezone=True), server_default=func.now())
    payment_method = Column(String(50))
    reference = Column(String(100))
    notes = Column(Text)
    
    # Relations
    reservation = relationship("Reservation", back_populates="payments")
    
    def __repr__(self):
        """Représentation textuelle du paiement."""
        return f"<Payment: {self.amount} € ({self.payment_method})>"
