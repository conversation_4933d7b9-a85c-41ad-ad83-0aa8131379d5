import unittest, os, sys
from PyQt5.QtWidgets import QApplication

# add wepro to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

class TestSettingsScreen(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls._app = QApplication.instance() or QApplication([])

    def test_settings_screen_build_ar_en_fr(self):
        from travelpro.settings.settings_manager import SettingsManager
        from travelpro.core.translations import translation_manager as tr

        for code in ['ar','en','fr']:
            tr.set_language(code)
            w = SettingsManager()
            # minimal smoke: ensure widget created and has tabs
            self.assertTrue(w.tabs.count() >= 3)
            w.deleteLater()

if __name__ == '__main__':
    unittest.main()

