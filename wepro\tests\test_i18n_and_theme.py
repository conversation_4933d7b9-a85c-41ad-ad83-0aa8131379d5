import unittest
import sys, os
# Ensure wepro/ is on sys.path so 'travelpro' resolves to wepro/travelpro
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from travelpro.core.translations import translation_manager as tr

class TestI18nAndTheme(unittest.TestCase):
    def test_language_overlay(self):
        tr.set_language('en')
        self.assertEqual(tr.get_main('dashboard', 'Tableau de bord'), 'Dashboard')
        tr.set_language('ar')
        self.assertEqual(tr.get_main('clients', 'Clients'), 'العملاء')
        tr.set_language('fr')
        self.assertEqual(tr.get_main('reports', 'Rapports'), 'Rapports')

if __name__ == '__main__':
    unittest.main()

