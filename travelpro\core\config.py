"""
TravelPro - Module Core
-----------------------
Ce module contient les fonctionnalités de base de l'application TravelPro,
notamment la configuration, l'authentification et la gestion de la base de données.
"""

import os
import json
import logging
from pathlib import Path

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class Config:
    """Gestion de la configuration de l'application."""
    
    _instance = None
    
    def __new__(cls):
        """Implémentation du pattern Singleton."""
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialisation de la configuration."""
        if self._initialized:
            return
            
        self._config_dir = os.path.join(str(Path.home()), '.travelpro')
        self._config_file = os.path.join(self._config_dir, 'config.json')
        self._config = {}
        
        # Valeurs par défaut
        self._defaults = {
            'db_path': os.path.join(self._config_dir, 'travelpro.db'),
            'theme': 'light',
            'language': 'fr',
            'backup_dir': os.path.join(self._config_dir, 'backups'),
            'log_level': 'INFO'
        }
        
        self._load_config()
        self._initialized = True
        
    def _load_config(self):
        """Charge la configuration depuis le fichier."""
        try:
            # Création du répertoire de configuration s'il n'existe pas
            if not os.path.exists(self._config_dir):
                os.makedirs(self._config_dir)
                logger.info(f"Répertoire de configuration créé: {self._config_dir}")
            
            # Chargement du fichier de configuration s'il existe
            if os.path.exists(self._config_file):
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logger.info(f"Configuration chargée depuis: {self._config_file}")
            else:
                # Création d'un fichier de configuration par défaut
                self._config = self._defaults.copy()
                self._save_config()
                logger.info(f"Fichier de configuration par défaut créé: {self._config_file}")
                
            # Création du répertoire de sauvegarde s'il n'existe pas
            backup_dir = self.get('backup_dir')
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
                logger.info(f"Répertoire de sauvegarde créé: {backup_dir}")
                
        except Exception as e:
            logger.error(f"Erreur lors du chargement de la configuration: {str(e)}")
            self._config = self._defaults.copy()
    
    def _save_config(self):
        """Sauvegarde la configuration dans le fichier."""
        try:
            with open(self._config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4)
            logger.info(f"Configuration sauvegardée dans: {self._config_file}")
        except Exception as e:
            logger.error(f"Erreur lors de la sauvegarde de la configuration: {str(e)}")
    
    def get(self, key, default=None):
        """Récupère une valeur de configuration."""
        return self._config.get(key, self._defaults.get(key, default))
    
    def set(self, key, value):
        """Définit une valeur de configuration."""
        self._config[key] = value
        self._save_config()
        
    def reset(self):
        """Réinitialise la configuration aux valeurs par défaut."""
        self._config = self._defaults.copy()
        self._save_config()
