"""
TravelPro - Système de traduction complet
Système de traduction pour supporter le français intégralement
"""

class FrenchTranslations:
    """Traductions complètes en français pour TravelPro."""
    
    # Interface principale
    MAIN_INTERFACE = {
        # Titre de l'application
        "app_title": "TravelPro - Système de Gestion d'Agence de Voyage",
        "app_subtitle": "Solution Professionnelle pour Agences de Voyage",
        
        # Menu principal
        "dashboard": "Tableau de Bord",
        "clients": "Clients",
        "sales": "Ventes",
        "invoices": "Factures",
        "suppliers": "Fournisseurs",
        "payments": "Paiements",
        "purchases": "Achats",
        "reports": "Rapports",
        "backup": "Sauvegarde",
        "bank": "Banque",
        "users": "Utilisateurs",
        "settings": "Paramètres",
        
        # Boutons communs
        "add": "Ajouter",
        "edit": "Modifier",
        "delete": "Supprimer",
        "save": "Enregistrer",
        "cancel": "Annuler",
        "search": "Rechercher",
        "filter": "Filtrer",
        "export": "Exporter",
        "import": "Importer",
        "print": "Imprimer",
        "refresh": "Actualiser",
        "close": "Fermer",
        "ok": "OK",
        "yes": "Oui",
        "no": "Non",
        
        # Navigation
        "previous": "Précédent",
        "next": "Suivant",
        "first": "Premier",
        "last": "Dernier",
        "page": "Page",
        "of": "de",
        
        # États
        "loading": "Chargement...",
        "saving": "Enregistrement...",
        "processing": "Traitement...",
        "completed": "Terminé",
        "failed": "Échec",
        "success": "Succès",
        "error": "Erreur",
        "warning": "Avertissement",
        "info": "Information",
        "description": "Description",
        "clear": "Effacer",
        "notes": "Notes",
    }
    
    # Système de connexion
    LOGIN_SYSTEM = {
        "login_title": "Connexion à TravelPro",
        "username": "Nom d'utilisateur",
        "password": "Mot de passe",
        "login": "Se connecter",
        "logout": "Se déconnecter",
        "remember_me": "Se souvenir de moi",
        "forgot_password": "Mot de passe oublié ?",
        "invalid_credentials": "Nom d'utilisateur ou mot de passe incorrect",
        "login_success": "Connexion réussie",
        "login_failed": "Échec de la connexion",
        "session_expired": "Session expirée",
        "welcome_back": "Bon retour",
        "please_login": "Veuillez vous connecter",
    }
    
    # Gestion des clients
    CLIENT_MANAGEMENT = {
        "client_management": "Gestion des Clients",
        "add_client": "Ajouter un Client",
        "edit_client": "Modifier le Client",
        "client_details": "Détails du Client",
        "client_list": "Liste des Clients",
        "client_search": "Recherche de Client",
        
        # Champs client
        "client_id": "ID Client",
        "first_name": "Prénom",
        "last_name": "Nom de famille",
        "full_name": "Nom complet",
        "email": "Email",
        "phone": "Téléphone",
        "address": "Adresse",
        "city": "Ville",
        "postal_code": "Code postal",
        "country": "Pays",
        "date_of_birth": "Date de naissance",
        "passport_number": "Numéro de passeport",
        "nationality": "Nationalité",
        "client_type": "Type de client",
        "vip_status": "Statut VIP",
        "notes": "Notes",
        "created_date": "Date de création",
        "last_updated": "Dernière mise à jour",
        
        # Types de clients
        "individual": "Particulier",
        "corporate": "Entreprise",
        "vip": "VIP",
        "regular": "Régulier",
        "new": "Nouveau",
        
        # Messages
        "client_added": "Client ajouté avec succès",
        "client_updated": "Client mis à jour avec succès",
        "client_deleted": "Client supprimé avec succès",
        "client_not_found": "Client non trouvé",
        "duplicate_client": "Client déjà existant",
    }
    
    # Gestion des ventes
    SALES_MANAGEMENT = {
        "sales_management": "Gestion des Ventes",
        "add_sale": "Ajouter une Vente",
        "edit_sale": "Modifier la Vente",
        "sale_details": "Détails de la Vente",
        "sales_list": "Liste des Ventes",
        "sales_report": "Rapport des Ventes",
        
        # Champs vente
        "sale_id": "ID Vente",
        "sale_date": "Date de vente",
        "client": "Client",
        "service": "Service",
        "destination": "Destination",
        "departure_date": "Date de départ",
        "return_date": "Date de retour",
        "adults": "Adultes",
        "children": "Enfants",
        "infants": "Bébés",
        "total_amount": "Montant total",
        "paid_amount": "Montant payé",
        "remaining_amount": "Montant restant",
        "payment_status": "Statut de paiement",
        "sale_status": "Statut de la vente",
        "commission": "Commission",
        "profit": "Bénéfice",

        # Champs supplémentaires
        "sales_overview": "Vue d'ensemble des Ventes",
        "sales_history": "Historique des Ventes",
        "sales_statistics": "Statistiques des Ventes",
        "today_sales": "Ventes d'aujourd'hui",
        "month_sales": "Ventes du mois",
        "total_sales": "Total des ventes",
        "recent_sales": "Ventes Récentes",
        "quick_actions": "Actions Rapides",
        "new_sale_data": "Données de Nouvelle Vente",
        "service_type": "Type de Service",
        "service_details": "Détails du Service",

        # Statuts
        "pending": "En attente",
        "confirmed": "Confirmé",
        "cancelled": "Annulé",
        "completed": "Terminé",
        "paid": "Payé",
        "partially_paid": "Partiellement payé",
        "unpaid": "Non payé",

        
        "advanced_sales_system": "Système de Ventes Avancé",
        "sales_method_name": "Nom de la méthode de vente",
        "save_sales_method": "Enregistrer la méthode de vente",
        "report_options": "Options de Rapport",
        
        # Messages
        "sale_added": "Vente ajoutée avec succès",
        "sale_updated": "Vente mise à jour avec succès",
        "sale_deleted": "Vente supprimée avec succès",
        "sale_confirmed": "Vente confirmée",
        "sale_cancelled": "Vente annulée",
    }
    
    # Gestion des factures
    INVOICE_MANAGEMENT = {
        "invoice_management": "Gestion des Factures",
        "create_invoice": "Créer une Facture",
        "edit_invoice": "Modifier la Facture",
        "invoice_details": "Détails de la Facture",
        "invoice_list": "Liste des Factures",
        "invoice_preview": "Aperçu de la Facture",
        "print_invoice": "Imprimer la Facture",
        "send_invoice": "Envoyer la Facture",
        
        # Champs facture
        "invoice_number": "Numéro de facture",
        "invoice_date": "Date de facture",
        "due_date": "Date d'échéance",
        "bill_to": "Facturer à",
        "items": "Articles",
        "description": "Description",
        "quantity": "Quantité",
        "unit_price": "Prix unitaire",
        "subtotal": "Sous-total",
        "tax": "Taxe",
        "discount": "Remise",
        "total": "Total",
        "payment_terms": "Conditions de paiement",
        
        # Messages
        "invoice_created": "Facture créée avec succès",
        "invoice_updated": "Facture mise à jour avec succès",
        "invoice_sent": "Facture envoyée avec succès",
        "invoice_printed": "Facture imprimée",
    }
    
    # Tableau de bord
    DASHBOARD = {
        "dashboard_title": "Tableau de Bord",
        "welcome_message": "Bienvenue dans TravelPro",
        "overview": "Vue d'ensemble",
        "statistics": "Statistiques",
        "recent_activities": "Activités récentes",
        "quick_actions": "Actions rapides",
        
        # KPIs
        "total_sales": "Ventes totales",
        "monthly_revenue": "Revenus mensuels",
        "total_clients": "Total clients",
        "pending_payments": "Paiements en attente",
        "active_bookings": "Réservations actives",
        "conversion_rate": "Taux de conversion",
        "average_sale": "Vente moyenne",
        "profit_margin": "Marge bénéficiaire",
        
        # Graphiques
        "sales_chart": "Graphique des ventes",
        "revenue_chart": "Graphique des revenus",
        "client_distribution": "Répartition des clients",
        "monthly_trends": "Tendances mensuelles",
        "performance_metrics": "Métriques de performance",
        
        # Périodes
        "today": "Aujourd'hui",
        "this_week": "Cette semaine",
        "this_month": "Ce mois",
        "this_year": "Cette année",
        "last_month": "Mois dernier",
        "last_year": "Année dernière",
        "custom_period": "Période personnalisée",
    }
    
    # Gestion des fournisseurs
    SUPPLIER_MANAGEMENT = {
        "supplier_management": "Gestion des Fournisseurs",
        "title": "Gestion des Fournisseurs",
        "add_supplier": "Ajouter un Fournisseur",
        "new_supplier": "Nouveau Fournisseur",
        "edit_supplier": "Modifier le Fournisseur",
        "delete": "Supprimer",
        "edit": "Modifier",
        "contact": "Contacter",
        "import": "Importer",
        "export": "Exporter",
        "refresh": "Actualiser",
        "supplier_details": "Détails du Fournisseur",
        "supplier_list": "Liste des Fournisseurs",

        # Champs fournisseur
        "supplier_name": "Nom du fournisseur",
        "name": "Nom",
        "contact_person": "Personne de contact",
        "contact": "Contact",
        "email": "Email",
        "phone": "Téléphone",
        "supplier_type": "Type de fournisseur",
        "services_offered": "Services offerts",
        "services": "Services",
        "commission_rate": "Taux de commission",
        "payment_terms": "Conditions de paiement",
        "contract_start": "Début du contrat",
        "contract_end": "Fin du contrat",
        "rating": "Évaluation",
        "status": "Statut",
        "actions": "Actions",
        "details": "Détails",

        # Filtres et recherche
        "search": "Recherche:",
        "search_ph": "Rechercher par nom, contact, email...",
        "all_services": "Tous les services",
        "all": "Tous",
        "active": "Actif",
        "inactive": "Inactif",
        "suspended": "Suspendu",
        "service": "Service:",

        # Types de fournisseurs
        "airline": "Compagnie aérienne",
        "hotel": "Hôtel",
        "car_rental": "Location de voiture",
        "tour_operator": "Tour opérateur",
        "insurance": "Assurance",
        "visa_service": "Service de visa",

        # Messages
        "supplier_added": "Fournisseur ajouté avec succès",
        "supplier_updated": "Fournisseur mis à jour avec succès",
        "supplier_deleted": "Fournisseur supprimé avec succès",
    }

    # Gestion des paiements
    PAYMENT_MANAGEMENT = {
        "payment_management": "Gestion des Paiements",
        "title": "Gestion des Paiements et Caisse",
        "add_payment": "Ajouter un Paiement",
        "new_payment": "Nouveau Paiement",
        "payment_details": "Détails du Paiement",
        "payment_history": "Historique des Paiements",
        "payment_methods": "Méthodes de Paiement",
        "tab_payments": "Paiements",
        "tab_cashbox": "Caisse",
        "tab_reports": "Rapports",
        "export": "Exporter",
        "refresh": "Actualiser",
        "search": "Recherche:",
        "search_ph": "Rechercher par référence, montant...",
        "actions": "Actions",

        # Méthodes de paiement
        "cash": "Espèces",
        "card": "Carte Bancaire",
        "transfer": "Virement",
        "check": "Chèque",
        "other": "Autre",
        "all_methods": "Toutes Méthodes",
        "credit_card": "Carte de crédit",
        "debit_card": "Carte de débit",
        "bank_transfer": "Virement bancaire",
        "online_payment": "Paiement en ligne",

        # Statuts de paiement
        "all_status": "Tous Statuts",
        "pending": "En Attente",
        "completed": "Complété",
        "failed": "Échoué",
        "cancelled": "Annulé",

        # Champs paiement
        "date": "Date",
        "amount": "Montant",
        "method": "Méthode",
        "payment_method": "Méthode de paiement",
        "reference": "Référence",
        "reference_number": "Numéro de référence",
        "received_by": "Reçu par",
        "notes": "Notes",

        # Caisse
        "type": "Type",
        "category": "Catégorie",
        "description": "Description",

        # Messages
        "payment_recorded": "Paiement enregistré avec succès",
        "payment_updated": "Paiement mis à jour avec succès",
        "payment_deleted": "Paiement supprimé avec succès",
    }

    # Rapports
    REPORTS = {
        "reports": "Rapports",
        "title": "Rapports et Analyses Avancés",
        "generate": "Générer Rapport",
        "export": "Exporter",
        "report_type": "Type de rapport",
        "type": "Type:",
        "date_range": "Plage de dates",
        "from": "Du:",
        "to": "Au:",
        "from_date": "Date de début",
        "to_date": "Date de fin",

        # Tabs
        "tab_dashboard": "Tableau de Bord",
        "tab_revenue": "Analyse des Revenus",
        "tab_service": "Performance des Services",
        "tab_trends": "Tendances",

        # Types de rapports
        "client_statistics": "Statistiques Clients",
        "revenue_analysis": "Analyse des Revenus",
        "service_performance": "Performance des Services",
        "monthly_trends": "Tendances Mensuelles",

        # Tables & champs
        "service": "Service",
        "bookings": "Réservations",
        "revenue": "Revenus",
        "cost": "Coûts",
        "profit": "Bénéfice",
        "margin": "Marge %",
        "avg_price": "Prix Moyen",
        "avg_pax": "Passagers Moyens",
        "trends_analysis": "Analyse des Tendances",
        "month": "Mois",
        "pax": "Passagers",

        # Messages
        "report_generated": "Rapport généré avec succès",
        "report_exported": "Rapport exporté avec succès",
        "no_data_found": "Aucune donnée trouvée",
    }

    # Messages système
    SYSTEM_MESSAGES = {
        "confirm_delete": "Êtes-vous sûr de vouloir supprimer cet élément ?",
        "confirm_action": "Êtes-vous sûr de vouloir effectuer cette action ?",
        "operation_successful": "Opération réussie",
        "operation_failed": "Opération échouée",
        "data_saved": "Données enregistrées avec succès",
        "data_loaded": "Données chargées avec succès",
        "validation_error": "Erreur de validation",
        "required_field": "Ce champ est obligatoire",
        "invalid_format": "Format invalide",
        "database_error": "Erreur de base de données",
        "network_error": "Erreur de réseau",
        "permission_denied": "Permission refusée",
        "session_timeout": "Session expirée",
        "file_not_found": "Fichier non trouvé",
        "backup_created": "Sauvegarde créée avec succès",
        "backup_restored": "Sauvegarde restaurée avec succès",
        # Nouveaux messages génériques
        "welcome_title": "Bienvenue",
        "welcome_message": "Bienvenue dans TravelPro !",
        "error_title": "Erreur",
        "generic_error": "Une erreur s'est produite",
    }

    # Paramètres
    SETTINGS = {
        "settings": "Paramètres",
        "general_settings": "Paramètres généraux",
        "user_settings": "Paramètres utilisateur",
        "system_settings": "Paramètres système",
        "appearance": "Apparence",
        "language": "Langue",
        "theme": "Thème",
        "notifications": "Notifications",
        "backup_settings": "Paramètres de sauvegarde",
        "database_settings": "Paramètres de base de données",
        "email_settings": "Paramètres email",
        "print_settings": "Paramètres d'impression",

        # Champs supplémentaires utilisés في SettingsSection
        "currency_symbol": "Symbole de devise",
        "decimal_places": "Décimales",
        "show_tooltips": "Afficher les info-bulles",
        "remember_window_size": "Mémoriser la taille de la fenêtre",
        "auto_save_settings": "Enregistrement automatique des paramètres",
        "save_basic": "Enregistrer",

        # Thèmes
        "light_theme": "Thème clair",
        "dark_theme": "Thème sombre",
        "blue_theme": "Thème bleu",
        "green_theme": "Thème vert",
        
        # Langues
        "french": "Français",
        "arabic": "العربية",
        "english": "English",
        
        # Messages
        "settings_saved": "Paramètres enregistrés avec succès",
        "settings_reset": "Paramètres réinitialisés",
        "restart_required": "Redémarrage requis pour appliquer les changements",
        # Champs complémentaires utilisés في SettingsSection
        "company_info": "Informations sur l'entreprise",
        "company_name": "Nom de l'entreprise",
        "address": "Adresse",
        "phone": "Téléphone",
        "email": "Email",
        "website": "Site web",
        "default_commission": "Commission par défaut",
        "default_tax_rate": "Taux de taxe par défaut",
        "invoice_prefix": "Préfixe de facture",
        "working_hours_start": "Début des heures de travail",
        "working_hours_end": "Fin des heures de travail",
        "auto_backup": "Sauvegarde automatique",
        "backup_frequency": "Fréquence de sauvegarde",
        "daily": "Quotidien",
        "weekly": "Hebdomadaire",
        "monthly": "Mensuel",
        "show_language_selector": "Afficher le sélecteur de langue au démarrage",
    }

import os
import json

class TranslationManager:
    """Gestionnaire de traductions pour TravelPro avec surcouches multilingues."""

    def __init__(self, language="french"):
        self.current_language = language
        self.translations = FrenchTranslations()  # Base FR
        self.overlay = {}  # Surcouche (EN/AR) facultative
        self._load_overlay(language)

    def _lang_code(self, language: str) -> str:
        lang = (language or "").strip().lower()
        if lang in ("fr", "french", "français", "francais"):  # base par défaut
            return "fr"
        if lang in ("ar", "arabic", "arabe", "العربية"):
            return "ar"
        if lang in ("en", "english", "anglais"):
            return "en"
        return "fr"

    def _load_overlay(self, language: str) -> None:
        """Charger la surcouche JSON si disponible pour la langue choisie."""
        code = self._lang_code(language)
        self.overlay = {}
        if code == "fr":
            return  # FR est déjà la base
        try:
            here = os.path.dirname(os.path.abspath(__file__))
            path = os.path.join(here, "i18n", f"{code}.json")
            if os.path.exists(path):
                with open(path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    # Normaliser les clés de catégories en MAJUSCULES
                    self.overlay = { (k.upper()): v for k, v in data.items() if isinstance(v, dict) }
        except Exception:
            # En cas d'erreur, garder overlay vide (fallback FR)
            self.overlay = {}

    def set_language(self, language: str) -> None:
        """Changer de langue à la volée (EN/AR) avec fallback FR."""
        self.current_language = language
        self._load_overlay(language)

    def get(self, category, key, default=None):
        """Obtenir une traduction avec priorité à la surcouche, sinon FR, sinon défaut."""
        try:
            cat = (category or "").upper()
            # 1) Surcouche si disponible
            if self.overlay:
                if cat in self.overlay and key in self.overlay[cat]:
                    return self.overlay[cat][key]
            # 2) Base FR
            category_dict = getattr(self.translations, cat)
            return category_dict.get(key, default or key)
        except Exception:
            return default or key

    def get_main(self, key, default=None):
        return self.get("MAIN_INTERFACE", key, default)

    def get_login(self, key, default=None):
        return self.get("LOGIN_SYSTEM", key, default)

    def get_client(self, key, default=None):
        return self.get("CLIENT_MANAGEMENT", key, default)

    def get_sales(self, key, default=None):
        return self.get("SALES_MANAGEMENT", key, default)

    def get_invoice(self, key, default=None):
        return self.get("INVOICE_MANAGEMENT", key, default)

    def get_dashboard(self, key, default=None):
        return self.get("DASHBOARD", key, default)

    def get_supplier(self, key, default=None):
        return self.get("SUPPLIER_MANAGEMENT", key, default)

    def get_payment(self, key, default=None):
        return self.get("PAYMENT_MANAGEMENT", key, default)

    def get_reports(self, key, default=None):
        return self.get("REPORTS", key, default)

    def get_system(self, key, default=None):
        return self.get("SYSTEM_MESSAGES", key, default)

    def get_settings(self, key, default=None):
        return self.get("SETTINGS", key, default)

# Instance globale du gestionnaire de traductions
translation_manager = TranslationManager("french")
