# -*- coding: utf-8 -*-
"""
TravelPro - Gestionnaire d'Erreurs Avancé
Système de gestion d'erreurs robuste avec logging et récupération automatique
"""

import sys
import traceback
import logging
import functools
from typing import Any, Callable, Optional, Type, Union
from datetime import datetime
import os

class TravelProErrorHandler:
    """Gestionnaire d'erreurs centralisé pour TravelPro."""
    
    def __init__(self, log_file: str = "logs/travelpro_errors.log"):
        self.log_file = log_file
        self.setup_logging()
    
    def setup_logging(self):
        """Configuration du système de logging."""
        # C<PERSON>er le dossier logs s'il n'existe pas
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # Configuration du logger
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger('TravelPro')
    
    def log_error(self, error: Exception, context: str = ""):
        """Enregistrer une erreur avec contexte."""
        error_msg = f"ERREUR dans {context}: {str(error)}"
        self.logger.error(error_msg)
        self.logger.error(f"Traceback: {traceback.format_exc()}")
    
    def safe_import(self, module_name: str, fallback: Any = None, context: str = ""):
        """Importation sécurisée avec fallback."""
        try:
            return __import__(module_name)
        except ImportError as e:
            self.log_error(e, f"Import {module_name} - {context}")
            self.logger.warning(f"Utilisation du fallback pour {module_name}")
            return fallback
    
    def safe_call(self, func: Callable, *args, fallback: Any = None, context: str = "", **kwargs):
        """Appel sécurisé de fonction avec fallback."""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.log_error(e, f"Appel {func.__name__} - {context}")
            return fallback
    
    def handle_critical_error(self, error: Exception, context: str = ""):
        """Gestion des erreurs critiques."""
        self.log_error(error, f"CRITIQUE - {context}")
        
        # Tentative de sauvegarde d'urgence
        try:
            self.emergency_backup()
        except Exception as backup_error:
            self.logger.error(f"Échec de la sauvegarde d'urgence: {backup_error}")
    
    def emergency_backup(self):
        """Sauvegarde d'urgence en cas d'erreur critique."""
        try:
            import shutil
            from datetime import datetime
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"emergency_backup_{timestamp}.db"
            
            if os.path.exists("wepro.db"):
                shutil.copy2("wepro.db", f"backups/{backup_name}")
                self.logger.info(f"Sauvegarde d'urgence créée: {backup_name}")
        except Exception as e:
            self.logger.error(f"Impossible de créer la sauvegarde d'urgence: {e}")

# Instance globale du gestionnaire d'erreurs
error_handler = TravelProErrorHandler()

def safe_import_decorator(fallback=None, context=""):
    """Décorateur pour importation sécurisée."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ImportError as e:
                error_handler.log_error(e, f"Import decorator - {context}")
                return fallback
        return wrapper
    return decorator

def error_handler_decorator(fallback=None, context="", critical=False):
    """Décorateur pour gestion d'erreurs."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                if critical:
                    error_handler.handle_critical_error(e, f"{func.__name__} - {context}")
                else:
                    error_handler.log_error(e, f"{func.__name__} - {context}")
                return fallback
        return wrapper
    return decorator

class SafeImportManager:
    """Gestionnaire d'importations sécurisées."""
    
    @staticmethod
    def import_pyqt5():
        """Importation sécurisée de PyQt5."""
        try:
            from PyQt5.QtWidgets import QApplication, QMainWindow, QWidget
            from PyQt5.QtCore import Qt, QTimer
            from PyQt5.QtGui import QFont, QIcon
            return True, None
        except ImportError as e:
            error_handler.log_error(e, "Import PyQt5")
            return False, str(e)
    
    @staticmethod
    def import_qtawesome():
        """Importation sécurisée de QtAwesome."""
        try:
            import qtawesome as qta
            return qta, None
        except ImportError as e:
            error_handler.log_error(e, "Import QtAwesome")
            # Fallback: créer un objet mock
            class MockQtAwesome:
                def icon(self, name, **kwargs):
                    return None
            return MockQtAwesome(), str(e)
    
    @staticmethod
    def import_sqlalchemy():
        """Importation sécurisée de SQLAlchemy."""
        try:
            from sqlalchemy import create_engine
            from sqlalchemy.orm import sessionmaker
            return True, None
        except ImportError as e:
            error_handler.log_error(e, "Import SQLAlchemy")
            return False, str(e)
    
    @staticmethod
    def import_database_models():
        """Importation sécurisée des modèles de base de données."""
        try:
            from models.user import User
            from models.client import Client
            from models.service import Service
            return True, None
        except ImportError as e:
            error_handler.log_error(e, "Import Database Models")
            return False, str(e)

class DatabaseErrorHandler:
    """Gestionnaire d'erreurs spécifique à la base de données."""
    
    @staticmethod
    def handle_connection_error(error: Exception):
        """Gestion des erreurs de connexion à la base de données."""
        error_handler.log_error(error, "Database Connection")
        
        # Tentatives de récupération
        try:
            # Vérifier si le fichier de base de données existe
            if not os.path.exists("wepro.db"):
                error_handler.logger.warning("Base de données manquante, création...")
                DatabaseErrorHandler.create_emergency_database()
        except Exception as recovery_error:
            error_handler.log_error(recovery_error, "Database Recovery")
    
    @staticmethod
    def create_emergency_database():
        """Créer une base de données d'urgence."""
        try:
            from database import init_db
            init_db()
            error_handler.logger.info("Base de données d'urgence créée")
        except Exception as e:
            error_handler.log_error(e, "Emergency Database Creation")

class UIErrorHandler:
    """Gestionnaire d'erreurs spécifique à l'interface utilisateur."""
    
    @staticmethod
    def handle_widget_error(error: Exception, widget_name: str = ""):
        """Gestion des erreurs de widgets."""
        error_handler.log_error(error, f"UI Widget - {widget_name}")
        
        # Tentative de récupération de l'interface
        try:
            UIErrorHandler.show_error_dialog(str(error))
        except Exception as dialog_error:
            error_handler.log_error(dialog_error, "Error Dialog")
    
    @staticmethod
    def show_error_dialog(message: str):
        """Afficher un dialogue d'erreur."""
        try:
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Critical)
            try:
                from travelpro.core.translations import translation_manager as tr
                _title = tr.get_system('error_title', 'Erreur')
                _text = tr.get_system('generic_error', "Une erreur s'est produite")
            except Exception:
                _title = 'Erreur'
                _text = "Une erreur s'est produite"
            msg.setWindowTitle(f"TravelPro - {_title}")
            msg.setText(_text)
            msg.setDetailedText(message)
            msg.exec_()
        except Exception:
            # Fallback: affichage console
            print(f"ERREUR TravelPro: {message}")

# Fonctions utilitaires globales
def safe_execute(func: Callable, *args, fallback=None, context="", **kwargs):
    """Exécution sécurisée d'une fonction."""
    return error_handler.safe_call(func, *args, fallback=fallback, context=context, **kwargs)

def log_info(message: str):
    """Log d'information."""
    error_handler.logger.info(message)

def log_warning(message: str):
    """Log d'avertissement."""
    error_handler.logger.warning(message)

def log_error(message: str):
    """Log d'erreur."""
    error_handler.logger.error(message)
