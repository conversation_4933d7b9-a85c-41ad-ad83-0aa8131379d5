import unittest
import sys, os
from PyQt5.QtWidgets import QApplication

# Ensure wepro/ is on sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from travelpro.core.translations import translation_manager as tr

class TestI18nExtended(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        if QApplication.instance() is None:
            cls._app = QApplication(sys.argv)

    def test_suppliers_headers_update(self):
        from travelpro.ui.suppliers_management import SuppliersManagementSection
        # Start in English
        tr.set_language('en')
        w = SuppliersManagementSection()
        # Check a couple of headers
        headers = [w.suppliers_table.horizontalHeaderItem(i).text() for i in range(w.suppliers_table.columnCount())]
        self.assertIn('Name', headers[0])
        self.assertIn('Status', headers[5])
        # Switch to Arabic and refresh
        tr.set_language('ar')
        w.refresh_texts('ar')
        headers_ar = [w.suppliers_table.horizontalHeaderItem(i).text() for i in range(w.suppliers_table.columnCount())]
        self.assertIn('الاسم', headers_ar[0])
        self.assertIn('الحالة', headers_ar[5])

    def test_payments_filters_update(self):
        from travelpro.ui.payments_cashbox_management import PaymentsCashboxSection
        tr.set_language('en')
        w = PaymentsCashboxSection(db_session_provider=lambda: iter(()))
        # English default
        self.assertTrue(w.payment_method_filter.itemText(0).lower().startswith('all'))
        # Switch to Arabic
        tr.set_language('ar')
        w.refresh_texts('ar')
        self.assertIn('ال', w.payment_method_filter.itemText(0))

    def test_settings_company_group_label(self):
        # Light-weight assertion on translations without constructing heavy UI
        tr.set_language('en')
        self.assertIn('Company', tr.get_settings('company_info', 'Company Information'))
        tr.set_language('ar')
        self.assertTrue(tr.get_settings('company_info', 'معلومات الشركة').startswith('م'))

if __name__ == '__main__':
    unittest.main()

