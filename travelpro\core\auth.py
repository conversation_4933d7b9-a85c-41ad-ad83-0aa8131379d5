"""
TravelPro - Module d'authentification
-------------------------------------
Ce module gère l'authentification des utilisateurs et les autorisations.
"""

import logging
import bcrypt
import jwt
import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.sql import func

from travelpro.core.database import Base, Database

logger = logging.getLogger(__name__)

class User(Base):
    """Modèle de données pour les utilisateurs."""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    full_name = Column(String)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def verify_password(self, password):
        """Vérifie si le mot de passe fourni correspond au hash stocké."""
        return bcrypt.checkpw(password.encode('utf-8'), self.hashed_password.encode('utf-8'))


class AuthManager:
    """Gestionnaire d'authentification pour TravelPro."""
    
    _instance = None
    
    def __new__(cls):
        """Implémentation du pattern Singleton."""
        if cls._instance is None:
            cls._instance = super(AuthManager, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """Initialisation du gestionnaire d'authentification."""
        if self._initialized:
            return
            
        self.db = Database()
        self.secret_key = "travelpro_secret_key"  # À remplacer par une clé sécurisée en production
        self._initialized = True
    
    def create_user(self, username, email, password, full_name="", is_admin=False):
        """Crée un nouvel utilisateur."""
        session = self.db.get_session()
        try:
            # Vérification si l'utilisateur existe déjà
            existing_user = session.query(User).filter(
                (User.username == username) | (User.email == email)
            ).first()
            
            if existing_user:
                logger.warning(f"Tentative de création d'un utilisateur existant: {username}")
                return None
            
            # Hachage du mot de passe
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # Création de l'utilisateur
            user = User(
                username=username,
                email=email,
                hashed_password=hashed_password,
                full_name=full_name,
                is_admin=is_admin
            )
            
            session.add(user)
            session.commit()
            session.refresh(user)
            
            logger.info(f"Utilisateur créé avec succès: {username}")
            return user
        except Exception as e:
            session.rollback()
            logger.error(f"Erreur lors de la création de l'utilisateur: {str(e)}")
            return None
        finally:
            self.db.close_session(session)
    
    def authenticate(self, username, password):
        """Authentifie un utilisateur et retourne un token JWT si réussi."""
        session = self.db.get_session()
        try:
            # Recherche de l'utilisateur
            user = session.query(User).filter(User.username == username).first()
            
            if not user or not user.is_active:
                logger.warning(f"Tentative d'authentification avec un utilisateur inexistant ou inactif: {username}")
                return None
            
            # Vérification du mot de passe
            if not user.verify_password(password):
                logger.warning(f"Tentative d'authentification avec mot de passe incorrect: {username}")
                return None
            
            # Génération du token JWT
            payload = {
                'user_id': user.id,
                'username': user.username,
                'is_admin': user.is_admin,
                'exp': datetime.datetime.utcnow() + datetime.timedelta(days=1)  # Expiration après 1 jour
            }
            
            token = jwt.encode(payload, self.secret_key, algorithm='HS256')
            
            logger.info(f"Utilisateur authentifié avec succès: {username}")
            return token
        except Exception as e:
            logger.error(f"Erreur lors de l'authentification: {str(e)}")
            return None
        finally:
            self.db.close_session(session)
    
    def verify_token(self, token):
        """Vérifie un token JWT et retourne les informations de l'utilisateur si valide."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token expiré")
            return None
        except jwt.InvalidTokenError:
            logger.warning("Token invalide")
            return None
    
    def get_user_by_id(self, user_id):
        """Récupère un utilisateur par son ID."""
        session = self.db.get_session()
        try:
            user = session.query(User).filter(User.id == user_id).first()
            return user
        except Exception as e:
            logger.error(f"Erreur lors de la récupération de l'utilisateur: {str(e)}")
            return None
        finally:
            self.db.close_session(session)
    
    def create_admin_if_not_exists(self):
        """Crée un utilisateur administrateur par défaut si aucun n'existe."""
        session = self.db.get_session()
        try:
            # Vérification si un admin existe déjà
            admin_exists = session.query(User).filter(User.is_admin == True).first() is not None
            
            if not admin_exists:
                # Création de l'admin par défaut
                self.create_user(
                    username="admin",
                    email="<EMAIL>",
                    password="admin123",  # À changer en production
                    full_name="Administrateur",
                    is_admin=True
                )
                logger.info("Utilisateur administrateur par défaut créé")
        except Exception as e:
            logger.error(f"Erreur lors de la vérification/création de l'admin: {str(e)}")
        finally:
            self.db.close_session(session)
