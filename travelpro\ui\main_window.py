"""
TravelPro - Interface utilisateur principale
--------------------------------------------
Ce module contient la fenêtre principale de l'application et les composants UI de base.
"""

import sys
import logging
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QLabel, QStatusBar, QToolBar, QAction, QMenu,
    QTabWidget, QSplitter, QFrame, QMessageBox
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QIcon, QFont, QAction

from travelpro.core.config import Config

logger = logging.getLogger(__name__)

class MainWindow(QMainWindow):
    """Fenêtre principale de l'application TravelPro."""
    
    def __init__(self):
        """Initialisation de la fenêtre principale."""
        super().__init__()
        
        self.config = Config()
        
        # Configuration de la fenêtre
        self.setWindowTitle("TravelPro - Gestion d'Agence de Voyages")
        self.setMinimumSize(1024, 768)
        
        # Création des widgets et du layout
        self.setup_ui()
        
        # Connexion des signaux
        self.connect_signals()
        
        logger.info("Interface utilisateur principale initialisée")
    
    def setup_ui(self):
        """Configure l'interface utilisateur."""
        # Barre de statut
        self.statusBar = QStatusBar()
        self.setStatusBar(self.statusBar)
        self.statusBar.showMessage("Prêt")
        
        # Barre d'outils
        self.toolbar = QToolBar("Barre d'outils principale")
        self.toolbar.setMovable(False)
        self.addToolBar(self.toolbar)
        
        # Actions de la barre d'outils
        self.action_new_client = QAction("Nouveau client", self)
        self.action_new_booking = QAction("Nouvelle réservation", self)
        self.action_settings = QAction("Paramètres", self)
        
        self.toolbar.addAction(self.action_new_client)
        self.toolbar.addAction(self.action_new_booking)
        self.toolbar.addSeparator()
        self.toolbar.addAction(self.action_settings)
        
        # Menu principal
        self.menu_bar = self.menuBar()
        
        # Menu Fichier
        self.file_menu = self.menu_bar.addMenu("Fichier")
        self.file_menu.addAction(self.action_new_client)
        self.file_menu.addAction(self.action_new_booking)
        self.file_menu.addSeparator()
        self.file_menu.addAction(self.action_settings)
        self.file_menu.addSeparator()
        
        self.action_exit = QAction("Quitter", self)
        self.file_menu.addAction(self.action_exit)
        
        # Menu Édition
        self.edit_menu = self.menu_bar.addMenu("Édition")
        self.action_cut = QAction("Couper", self)
        self.action_copy = QAction("Copier", self)
        self.action_paste = QAction("Coller", self)
        
        self.edit_menu.addAction(self.action_cut)
        self.edit_menu.addAction(self.action_copy)
        self.edit_menu.addAction(self.action_paste)
        
        # Menu Modules
        self.modules_menu = self.menu_bar.addMenu("Modules")
        self.action_midoffice = QAction("Mid-Office", self)
        self.action_products = QAction("Produits", self)
        self.action_transport = QAction("Transport", self)
        self.action_clients = QAction("Clients & Fidélité", self)
        self.action_reporting = QAction("Reporting", self)
        
        self.modules_menu.addAction(self.action_midoffice)
        self.modules_menu.addAction(self.action_products)
        self.modules_menu.addAction(self.action_transport)
        self.modules_menu.addAction(self.action_clients)
        self.modules_menu.addAction(self.action_reporting)
        
        # Menu Aide
        self.help_menu = self.menu_bar.addMenu("Aide")
        self.action_help = QAction("Documentation", self)
        self.action_about = QAction("À propos", self)
        
        self.help_menu.addAction(self.action_help)
        self.help_menu.addAction(self.action_about)
        
        # Widget central avec splitter
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        
        self.main_layout = QHBoxLayout(self.central_widget)
        
        # Splitter pour sidebar et contenu principal
        self.splitter = QSplitter(Qt.Orientation.Horizontal)
        self.main_layout.addWidget(self.splitter)
        
        # Sidebar (panneau de navigation)
        self.sidebar = QFrame()
        self.sidebar.setFrameShape(QFrame.Shape.StyledPanel)
        self.sidebar.setMinimumWidth(200)
        self.sidebar.setMaximumWidth(300)
        
        self.sidebar_layout = QVBoxLayout(self.sidebar)
        
        # Boutons de navigation
        self.nav_buttons = []
        nav_items = [
            ("Tableau de bord", self.on_dashboard_clicked),
            ("Clients", self.on_clients_clicked),
            ("Réservations", self.on_bookings_clicked),
            ("Produits", self.on_products_clicked),
            ("Transport", self.on_transport_clicked),
            ("Facturation", self.on_invoices_clicked),
            ("Rapports", self.on_reports_clicked)
        ]
        
        for text, callback in nav_items:
            button = QPushButton(text)
            button.setMinimumHeight(40)
            button.clicked.connect(callback)
            self.sidebar_layout.addWidget(button)
            self.nav_buttons.append(button)
        
        # Espace flexible
        self.sidebar_layout.addStretch()
        
        # Informations utilisateur
        self.user_frame = QFrame()
        self.user_layout = QVBoxLayout(self.user_frame)
        
        self.user_label = QLabel("Connecté en tant que")
        self.user_name = QLabel("Admin")
        self.user_name.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        
        self.user_layout.addWidget(self.user_label)
        self.user_layout.addWidget(self.user_name)
        
        self.sidebar_layout.addWidget(self.user_frame)
        
        # Zone de contenu principal avec onglets
        self.content_area = QFrame()
        self.content_area.setFrameShape(QFrame.Shape.StyledPanel)
        
        self.content_layout = QVBoxLayout(self.content_area)
        
        self.tab_widget = QTabWidget()
        self.content_layout.addWidget(self.tab_widget)
        
        # Onglet Tableau de bord (par défaut)
        self.dashboard_tab = QWidget()
        self.tab_widget.addTab(self.dashboard_tab, "Tableau de bord")
        
        self.dashboard_layout = QVBoxLayout(self.dashboard_tab)
        self.dashboard_title = QLabel("Tableau de bord")
        self.dashboard_title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        self.dashboard_layout.addWidget(self.dashboard_title)
        
        # Placeholder pour le contenu du tableau de bord
        self.dashboard_content = QLabel("Contenu du tableau de bord à venir...")
        self.dashboard_layout.addWidget(self.dashboard_content)
        self.dashboard_layout.addStretch()
        
        # Ajout des widgets au splitter
        self.splitter.addWidget(self.sidebar)
        self.splitter.addWidget(self.content_area)
        
        # Définir les proportions du splitter (20% sidebar, 80% contenu)
        self.splitter.setSizes([200, 800])
    
    def connect_signals(self):
        """Connecte les signaux aux slots."""
        self.action_exit.triggered.connect(self.close)
        self.action_about.triggered.connect(self.show_about_dialog)
    
    def show_about_dialog(self):
        """Affiche la boîte de dialogue À propos."""
        QMessageBox.about(
            self,
            "À propos de TravelPro",
            "TravelPro v0.1.0\n\n"
            "Logiciel de gestion d'agence de voyages\n"
            "Développé avec Python et PyQt6\n\n"
            "© 2025 Manus AI"
        )
    
    # Slots pour les boutons de navigation
    def on_dashboard_clicked(self):
        self.statusBar.showMessage("Tableau de bord")
        # Ici, on pourrait charger le contenu du tableau de bord
    
    def on_clients_clicked(self):
        self.statusBar.showMessage("Gestion des clients")
        # Ici, on pourrait ouvrir un nouvel onglet pour la gestion des clients
    
    def on_bookings_clicked(self):
        self.statusBar.showMessage("Gestion des réservations")
        # Ici, on pourrait ouvrir un nouvel onglet pour la gestion des réservations
    
    def on_products_clicked(self):
        self.statusBar.showMessage("Gestion des produits")
        # Ici, on pourrait ouvrir un nouvel onglet pour la gestion des produits
    
    def on_transport_clicked(self):
        self.statusBar.showMessage("Gestion des transports")
        # Ici, on pourrait ouvrir un nouvel onglet pour la gestion des transports
    
    def on_invoices_clicked(self):
        self.statusBar.showMessage("Facturation")
        # Ici, on pourrait ouvrir un nouvel onglet pour la facturation
    
    def on_reports_clicked(self):
        self.statusBar.showMessage("Rapports")
        # Ici, on pourrait ouvrir un nouvel onglet pour les rapports


def run_app():
    """Lance l'application TravelPro."""
    app = QApplication(sys.argv)
    
    # Configuration de l'application
    app.setApplicationName("TravelPro")
    app.setApplicationVersion("0.1.0")
    
    # Création et affichage de la fenêtre principale
    window = MainWindow()
    window.show()
    
    # Exécution de la boucle d'événements
    sys.exit(app.exec())
